#!/usr/bin/env python3
"""
Test unit-level Australia Excel integration

This test verifies that the Australia Excel data is correctly integrated
at the unit level with the proper time-series format.
"""

import pandas as pd
import os
from typing import Dict, List, Optional

def create_unit_level_integration_function():
    """Create a standalone version of the unit-level integration function"""
    
    # Load Excel data
    excel_path = "Australia_details.xlsx"
    if not os.path.exists(excel_path):
        print(f"❌ Australia Excel file not found: {excel_path}")
        return None
    
    df = pd.read_excel(excel_path)
    # Remove header row
    df = df.iloc[1:].reset_index(drop=True)
    
    # Create year column mapping
    year_columns = {}
    emission_columns = {}
    
    for i, col in enumerate(df.columns):
        if isinstance(col, int) and 2020 <= col <= 2024:  # Year columns
            year_columns[col] = i
            # Next column should be emission factor
            if i + 1 < len(df.columns):
                emission_columns[col] = i + 1
    
    def clean_plant_name(plant_name: str) -> str:
        """Clean plant name for better matching"""
        clean_name = plant_name.lower().strip()
        
        # Remove common power plant suffixes
        suffixes_to_remove = [
            'power station', 'power plant', 'generating station', 
            'generation station', 'thermal power station', 'coal power station'
        ]
        
        for suffix in suffixes_to_remove:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)].strip()
        
        return clean_name
    
    def find_plant_data(plant_name: str) -> Optional[Dict]:
        """Find plant data in Excel"""
        clean_plant_name_input = clean_plant_name(plant_name)
        
        # Try exact match first
        for idx, row in df.iterrows():
            excel_plant_name = str(row['Plant Name']).lower().strip()
            excel_clean_name = clean_plant_name(excel_plant_name)
            
            if clean_plant_name_input == excel_clean_name:
                return extract_plant_data(row)
        
        # Try fuzzy matching
        for idx, row in df.iterrows():
            excel_plant_name = str(row['Plant Name']).lower().strip()
            excel_clean_name = clean_plant_name(excel_plant_name)
            
            if clean_plant_name_input in excel_clean_name or excel_clean_name in clean_plant_name_input:
                print(f"🔍 Fuzzy match found: {row['Plant Name']}")
                return extract_plant_data(row)
        
        return None
    
    def extract_plant_data(row) -> Dict:
        """Extract plant data from Excel row"""
        plant_data = {
            "entity_name": str(row['Entity Name']),
            "plant_name": str(row['Plant Name']),
            "gross_power_generation": {},
            "emission_factor": {},
            "years_available": list(year_columns.keys())
        }
        
        # Extract year-wise data
        for year in year_columns.keys():
            try:
                # Get gross power generation
                gross_col_idx = year_columns[year]
                gross_value = row.iloc[gross_col_idx]
                
                if pd.notna(gross_value) and gross_value != 0:
                    plant_data["gross_power_generation"][str(year)] = {
                        "value": float(gross_value),
                        "unit": "MWh"
                    }
                
                # Get emission factor
                if year in emission_columns:
                    emission_col_idx = emission_columns[year]
                    emission_value = row.iloc[emission_col_idx]
                    
                    if pd.notna(emission_value) and emission_value != 0:
                        plant_data["emission_factor"][str(year)] = {
                            "value": float(emission_value),
                            "unit": "tCO2/MWh"
                        }
                        
            except (ValueError, IndexError) as e:
                print(f"⚠️ Error extracting data for year {year}: {e}")
                continue
        
        return plant_data
    
    def integrate_unit_data(unit_info: Dict, plant_name: str, country: str) -> Dict:
        """Integrate Australia Excel data into unit-level information"""
        # Only process Australian plants
        if country.lower() != "australia":
            return unit_info
        
        if not plant_name:
            return unit_info
        
        # Find Excel data for the plant
        excel_data = find_plant_data(plant_name)
        
        if excel_data is None:
            print(f"📊 No Australia Excel data found for unit in plant: {plant_name}")
            return unit_info
        
        unit_number = unit_info.get("unit_number", "unknown")
        print(f"✅ Integrating Australia Excel data for Unit {unit_number} in {plant_name}")
        
        # Convert Excel data to unit-level time-series format
        # Excel format: {"2020": {"value": 15756684.0, "unit": "MWh"}}
        # Unit format: [{"value": "15756684", "year": "2020"}, ...]
        
        # Integrate gross power generation data
        if "gross_power_generation" in excel_data and excel_data["gross_power_generation"]:
            unit_gross_generation = []
            for year, data in excel_data["gross_power_generation"].items():
                unit_gross_generation.append({
                    "value": str(int(data["value"])),  # Convert to string as expected by unit schema
                    "year": str(year)
                })
            
            # Sort by year (newest first)
            unit_gross_generation.sort(key=lambda x: int(x["year"]), reverse=True)
            
            unit_info["gross_power_generation"] = unit_gross_generation
            print(f"   📈 Added gross power generation data for {len(unit_gross_generation)} years")
            
            # Log the data for verification
            for entry in unit_gross_generation[:3]:  # Show first 3 entries
                print(f"      {entry['year']}: {entry['value']} MWh")
        
        # Integrate emission factor data
        if "emission_factor" in excel_data and excel_data["emission_factor"]:
            unit_emission_factor = []
            for year, data in excel_data["emission_factor"].items():
                unit_emission_factor.append({
                    "value": str(data["value"]),  # Convert to string as expected by unit schema
                    "year": str(year)
                })
            
            # Sort by year (newest first)
            unit_emission_factor.sort(key=lambda x: int(x["year"]), reverse=True)
            
            unit_info["emission_factor"] = unit_emission_factor
            print(f"   🌿 Added emission factor data for {len(unit_emission_factor)} years")
            
            # Log the data for verification
            for entry in unit_emission_factor[:3]:  # Show first 3 entries
                print(f"      {entry['year']}: {entry['value']} tCO2/MWh")
        
        # Add metadata about Excel integration to unit
        unit_info["australia_excel_integration"] = {
            "integrated": True,
            "excel_entity_name": excel_data.get("entity_name", ""),
            "excel_plant_name": excel_data.get("plant_name", ""),
            "years_available": excel_data.get("years_available", []),
            "data_source": "Australia_details.xlsx",
            "integration_level": "unit"
        }
        
        return unit_info
    
    return integrate_unit_data

def test_unit_level_integration():
    """Test unit-level integration functionality"""
    print("🧪 Unit-Level Australia Excel Integration Test")
    print("=" * 50)
    
    # Create the integration function
    integrate_unit_data = create_unit_level_integration_function()
    
    if integrate_unit_data is None:
        print("❌ Could not create integration function")
        return False
    
    # Mock unit data structure (similar to what combine_unit_data produces)
    mock_unit_data = {
        "sk": "unit#coal#1#plant#1",
        "unit_number": "1",
        "plant_id": "1",
        "plant_uid": "test-plant-uuid",
        "capacity": "660",
        "capacity_unit": "MW",
        "technology": "Super Critical",
        "boiler_type": "Pulverized Coal",
        "commencement_date": "2012-01-01T00:00:00.000Z",
        
        # Default time-series data (will be replaced by Excel data)
        "gross_power_generation": [
            {"value": "2500000", "year": "2023"},
            {"value": "2400000", "year": "2022"}
        ],
        "emission_factor": [
            {"value": "0.95", "year": "2023"},
            {"value": "0.97", "year": "2022"}
        ],
        
        # Other required fields
        "plf": [{"value": "65.0", "year": "2023"}],
        "PAF": [{"value": "85.0", "year": "2023"}],
        "auxiliary_power_consumed": [{"value": "8.5", "year": "2023"}],
        "fuel_type": [{"value": "Coal", "year": "2023"}]
    }
    
    # Test cases for different plants
    test_cases = [
        {
            "plant_name": "Bayswater Power Station",
            "country": "Australia",
            "description": "Australian coal plant (should integrate)"
        },
        {
            "plant_name": "Some Indian Plant",
            "country": "India", 
            "description": "Non-Australian plant (should skip)"
        },
        {
            "plant_name": "Unknown Australian Plant",
            "country": "Australia",
            "description": "Australian plant not in Excel (should skip)"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['description']}")
        print(f"   Plant: {test_case['plant_name']}")
        print(f"   Country: {test_case['country']}")
        
        # Create a copy of mock data for this test
        test_unit_data = mock_unit_data.copy()
        
        # Apply integration
        result = integrate_unit_data(
            unit_info=test_unit_data,
            plant_name=test_case['plant_name'],
            country=test_case['country']
        )
        
        # Check results
        if "australia_excel_integration" in result:
            integration_meta = result["australia_excel_integration"]
            print(f"   ✅ Integration applied!")
            print(f"      Excel entity: {integration_meta['excel_entity_name']}")
            print(f"      Excel plant: {integration_meta['excel_plant_name']}")
            print(f"      Years available: {integration_meta['years_available']}")
        else:
            print(f"   ⏭️ Integration skipped (as expected)")
            
            # Verify original data is preserved for non-Australian plants
            if test_case['country'].lower() != 'australia':
                if result["gross_power_generation"] == mock_unit_data["gross_power_generation"]:
                    print(f"   ✅ Original data preserved")
                else:
                    print(f"   ⚠️ Original data was modified unexpectedly")
    
    print(f"\n✅ Unit-level integration test completed successfully!")
    return True

if __name__ == "__main__":
    test_unit_level_integration()
