#!/usr/bin/env python3
"""
Check the exact column names in the Excel file
"""

import pandas as pd
from pathlib import Path

def check_exact_columns():
    """Check the exact column names"""
    print("🔍 Checking Exact Column Names in Excel")
    print("=" * 50)
    
    try:
        # Load Excel file
        excel_path = Path("Australia_details.xlsx")
        df = pd.read_excel(excel_path)
        
        print(f"📊 Excel file shape: {df.shape}")
        print()
        
        print("📋 Exact Column Names:")
        for i, col_name in enumerate(df.columns):
            print(f"  Column {i:2d}: '{col_name}'")
            
            # Check what keywords are in each column name
            col_str = str(col_name).lower()
            keywords_found = []
            
            # Check for year
            for year in range(2020, 2025):
                if str(year) in col_str:
                    keywords_found.append(f"year_{year}")
            
            # Check for power generation keywords
            power_keywords = ['gross', 'power', 'generation', 'gpower']
            for keyword in power_keywords:
                if keyword in col_str:
                    keywords_found.append(f"power_{keyword}")
            
            # Check for emission keywords
            emission_keywords = ['emission', 'factor']
            for keyword in emission_keywords:
                if keyword in col_str:
                    keywords_found.append(f"emission_{keyword}")
            
            if keywords_found:
                print(f"              Keywords: {', '.join(keywords_found)}")
        
        print()
        print("🔍 Testing Pattern Matching:")
        
        # Test current pattern matching logic
        year_columns = {}
        emission_columns = {}
        
        for i, col_name in enumerate(df.columns):
            col_str = str(col_name).lower()
            print(f"\nColumn {i}: '{col_name}' -> '{col_str}'")
            
            # Look for year patterns in column names
            for year in range(2020, 2025):
                year_str = str(year)
                if year_str in col_str:
                    print(f"  Found year {year}")
                    
                    # Current logic
                    if 'gross' in col_str or 'power' in col_str or 'generation' in col_str:
                        print(f"  ❌ Current logic: Would match as gross power generation")
                        year_columns[year] = i
                    elif 'emission' in col_str or 'factor' in col_str:
                        print(f"  ✅ Current logic: Would match as emission factor")
                        emission_columns[year] = i
                    
                    # Updated logic with 'gpower'
                    if 'gross' in col_str or 'gpower' in col_str or 'power' in col_str or 'generation' in col_str:
                        print(f"  ✅ Updated logic: Would match as gross power generation")
                    elif 'emission' in col_str or 'factor' in col_str:
                        print(f"  ✅ Updated logic: Would match as emission factor")
        
        print(f"\n📊 Current Logic Results:")
        print(f"  Year columns: {year_columns}")
        print(f"  Emission columns: {emission_columns}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking columns: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_exact_columns()
