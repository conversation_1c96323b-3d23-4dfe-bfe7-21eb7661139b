#!/usr/bin/env python3
"""
Test the full pipeline integration to verify gross power generation is included in unit JSON
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_full_pipeline():
    """Test the full pipeline integration"""
    print("🧪 Testing Full Pipeline Integration")
    print("=" * 50)
    
    try:
        # Import the combine_unit_data function (this is what's called in the real pipeline)
        from agent.unit_extraction_stages import combine_unit_data
        
        # Mock plant context (this is what would come from the plant extraction)
        plant_context = {
            'country': 'Australia',
            'plant_name': 'Bayswater Power Station',
            'plant_type': 'coal',
            'owner': 'AGL ENERGY LIMITED'
        }
        
        # Mock extracted data from the 4 extraction stages
        basic_info = {
            "unit_number": "1",
            "plant_id": "1",
            "capacity": "660",
            "capacity_unit": "MW",
            "technology": "Super Critical",
            "boiler_type": "Pulverized Coal",
            "commencement_date": "1985-01-01"
        }
        
        performance_metrics = {
            "heat_rate": "2200",
            "heat_rate_unit": "Kcal/kWh",
            "coal_unit_efficiency": "38.5",
            "plf": [{"value": "75", "year": "2023"}],
            "PAF": [{"value": "85", "year": "2023"}],
            "auxiliary_power_consumed": [{"value": "45", "year": "2023"}]
        }
        
        fuel_emissions = {
            "annual_operational_hours": 8760,
            "blending_percentage_of_biomass": 0.15,
            "emission_factor_coal": "0.89",
            "fuel_type": [{"value": "Coal", "year": "2023"}],
            "fgds_status": "Installed"
        }
        
        economic_data = {
            "capex": "1200000000",
            "capex_unit": "AUD",
            "opex": "45000000",
            "opex_unit": "AUD/year"
        }
        
        print("📋 Testing combine_unit_data with Australia Excel integration")
        print(f"   Plant: {plant_context['plant_name']}")
        print(f"   Country: {plant_context['country']}")
        print(f"   Unit: {basic_info['unit_number']}")
        print()
        
        # Prepare stage results as expected by combine_unit_data
        stage_results = [basic_info, performance_metrics, fuel_emissions, economic_data]

        # Add plant_id and plant_uid to plant_context as expected
        plant_context['plant_id'] = '1'
        plant_context['plant_uid'] = 'test-plant-uuid-123'

        # Call the actual combine_unit_data function (this is what the real pipeline calls)
        combined_data = combine_unit_data(
            stage_results=stage_results,
            unit_number="1",
            plant_context=plant_context
        )
        
        print("✅ combine_unit_data completed")
        print()
        
        # Check if Australia Excel integration was applied
        if "australia_excel_integration" in combined_data:
            integration_meta = combined_data["australia_excel_integration"]
            print(f"🇦🇺 Australia Excel Integration Applied:")
            print(f"   ✅ Integrated: {integration_meta['integrated']}")
            print(f"   🏢 Excel entity: {integration_meta['excel_entity_name']}")
            print(f"   🏭 Excel plant: {integration_meta['excel_plant_name']}")
            print(f"   📅 Years available: {integration_meta['years_available']}")
            print(f"   📊 Data source: {integration_meta['data_source']}")
            print()
        else:
            print(f"❌ No Australia Excel integration metadata found")
            print()
        
        # Check gross power generation in the final unit JSON
        if "gross_power_generation" in combined_data:
            gpg = combined_data["gross_power_generation"]
            print(f"📈 Gross Power Generation in Final Unit JSON:")
            print(f"   📊 Type: {type(gpg)}")
            print(f"   📊 Length: {len(gpg) if isinstance(gpg, list) else 'Not a list'}")
            
            if isinstance(gpg, list) and gpg:
                print(f"   📊 Sample entries:")
                for entry in gpg[:3]:  # Show first 3 entries
                    print(f"      {entry['year']}: {entry['value']} MWh")
                
                # Check for all expected years
                years_found = {entry['year'] for entry in gpg}
                expected_years = {'2020', '2021', '2022', '2023', '2024'}
                
                if expected_years.issubset(years_found):
                    print(f"   ✅ All expected years found: {sorted(expected_years)}")
                else:
                    missing_years = expected_years - years_found
                    print(f"   ❌ Missing years: {sorted(missing_years)}")
                
                # Specifically check for 2021
                has_2021 = '2021' in years_found
                if has_2021:
                    gpg_2021 = next(entry for entry in gpg if entry['year'] == '2021')
                    print(f"   🎉 2021 gross power generation: {gpg_2021['value']} MWh")
                else:
                    print(f"   ❌ 2021 gross power generation missing")
            else:
                print(f"   ❌ Gross power generation is empty or not a list")
            print()
        else:
            print(f"❌ No gross_power_generation field found in final unit JSON")
            print()
        
        # Check emission factor in the final unit JSON
        if "emission_factor" in combined_data:
            ef = combined_data["emission_factor"]
            print(f"🌿 Emission Factor in Final Unit JSON:")
            print(f"   📊 Type: {type(ef)}")
            print(f"   📊 Length: {len(ef) if isinstance(ef, list) else 'Not a list'}")
            
            if isinstance(ef, list) and ef:
                print(f"   📊 Sample entries:")
                for entry in ef[:3]:  # Show first 3 entries
                    print(f"      {entry['year']}: {entry['value']} tCO2/MWh")
                
                # Check for 2021
                years_found = {entry['year'] for entry in ef}
                has_2021 = '2021' in years_found
                if has_2021:
                    ef_2021 = next(entry for entry in ef if entry['year'] == '2021')
                    print(f"   🎉 2021 emission factor: {ef_2021['value']} tCO2/MWh")
                else:
                    print(f"   ❌ 2021 emission factor missing")
            else:
                print(f"   ❌ Emission factor is empty or not a list")
            print()
        else:
            print(f"❌ No emission_factor field found in final unit JSON")
            print()
        
        # Show a summary of all fields in the final unit JSON
        print(f"📋 Final Unit JSON Summary:")
        print(f"   Total fields: {len(combined_data)}")
        
        # Check for key fields
        key_fields = [
            "sk", "unit_number", "plant_id", "capacity", "technology",
            "gross_power_generation", "emission_factor", "australia_excel_integration"
        ]
        
        for field in key_fields:
            if field in combined_data:
                value = combined_data[field]
                if isinstance(value, list):
                    print(f"   ✅ {field}: list with {len(value)} entries")
                elif isinstance(value, dict):
                    print(f"   ✅ {field}: dict with {len(value)} keys")
                else:
                    print(f"   ✅ {field}: {value}")
            else:
                print(f"   ❌ {field}: missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during full pipeline test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_pipeline()
    
    if success:
        print(f"\n🎉 Full pipeline test completed!")
        print(f"✅ This shows exactly what the real pipeline produces.")
    else:
        print(f"\n❌ Full pipeline test failed!")
