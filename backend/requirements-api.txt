# FastAPI Requirements
# Additional requirements for the FastAPI wrapper layer

fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
websockets==12.0

# CORS support
python-cors==1.7.0

# Background task processing
celery==5.3.4  # Optional: for production-grade background processing
redis==5.0.1   # Optional: for job queue and caching

# Monitoring and logging
prometheus-client==0.19.0  # Optional: for metrics
structlog==23.2.0         # Optional: for structured logging

# All existing requirements are inherited from the main requirements.txt
# This file only adds FastAPI-specific dependencies
