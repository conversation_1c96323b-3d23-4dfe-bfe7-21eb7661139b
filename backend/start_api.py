#!/usr/bin/env python3
"""
FastAPI Server Startup Script

This script starts the FastAPI server for the Power Plant Extraction Pipeline.
It ensures all dependencies are properly initialized before starting the server.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        import fastapi
        import uvicorn
        import pydantic
        print("✅ FastAPI dependencies available")
        
        # Check existing pipeline dependencies
        from agent.graph import graph
        from agent.database_manager import get_database_manager
        from agent.entity_extraction_controller import EntityExtractionController
        print("✅ Pipeline dependencies available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install FastAPI requirements:")
        print("pip install -r requirements-api.txt")
        return False

def initialize_database():
    """Initialize database tables"""
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        db_manager.create_tables()
        
        if db_manager.test_connection():
            print("✅ Database initialized and connected")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    try:
        import uvicorn

        print("🚀 Starting FastAPI server...")
        print("📡 API will be available at: http://localhost:8000")
        print("📚 API documentation at: http://localhost:8000/docs")
        print("🔍 Health check at: http://localhost:8000/health")
        print("🔄 Server will start in 3 seconds...")

        # Start server with proper import string to fix reload warning
        uvicorn.run(
            "api.main:app",  # Use import string instead of app object
            host="0.0.0.0",
            port=8000,
            reload=True,  # Enable auto-reload for development
            log_level="info",
            access_log=True
        )

    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        print(f"Error details: {type(e).__name__}: {e}")
        return False

def main():
    """Main startup function"""
    print("🔧 Power Plant Extraction API - Starting Up")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Initialize database
    if not initialize_database():
        print("⚠️ Database initialization failed, but continuing...")
    
    # Start server
    print("\n🚀 Starting FastAPI Server...")
    start_server()

if __name__ == "__main__":
    main()
