#!/usr/bin/env python3
"""
Simple FastAPI Server Runner

Direct uvicorn command to start the FastAPI server
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting Power Plant Extraction API...")
    print("📡 Server will be available at: http://localhost:8000")
    print("📚 API docs at: http://localhost:8000/docs")
    print("🔍 Health check at: http://localhost:8000/health")
    print("=" * 50)
    
    # Start server directly with uvicorn
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0", 
        port=8000,
        reload=True,
        log_level="info"
    )
