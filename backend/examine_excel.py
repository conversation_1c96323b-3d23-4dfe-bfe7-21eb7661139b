#!/usr/bin/env python3
"""
Examine the Excel file structure to understand the current column layout
"""

import pandas as pd
from pathlib import Path

def examine_excel():
    """Examine the Excel file structure"""
    print("🔍 Examining Australia_details.xlsx")
    print("=" * 40)
    
    try:
        # Load Excel file
        excel_path = Path("Australia_details.xlsx")
        df = pd.read_excel(excel_path)
        
        print(f"📊 Excel file shape: {df.shape}")
        print()
        
        # Show first few rows to understand structure
        print("📋 First 3 rows of Excel file:")
        for row_idx in range(min(3, len(df))):
            print(f"\nRow {row_idx}:")
            for col_idx in range(min(12, len(df.columns))):
                val = df.iloc[row_idx, col_idx]
                print(f"  Column {col_idx:2d}: '{val}' (type: {type(val).__name__})")
        
        print(f"\n📋 Column names from pandas:")
        for i, col in enumerate(df.columns):
            print(f"  Column {i:2d}: '{col}' (type: {type(col).__name__})")
        
        # Check if row 0 contains headers
        print(f"\n🔍 Analyzing Row 0 (potential header row):")
        header_row = df.iloc[0]
        for i, val in enumerate(header_row):
            val_str = str(val).lower()
            has_year = any(str(year) in val_str for year in range(2020, 2025))
            has_keywords = any(keyword in val_str for keyword in ['gross', 'power', 'generation', 'emission', 'factor'])
            print(f"  Column {i:2d}: '{val}' -> has_year: {has_year}, has_keywords: {has_keywords}")
        
        # Check if row 1 contains data
        print(f"\n🔍 Analyzing Row 1 (potential data row):")
        if len(df) > 1:
            data_row = df.iloc[1]
            for i, val in enumerate(data_row):
                is_numeric = isinstance(val, (int, float)) and not pd.isna(val)
                print(f"  Column {i:2d}: '{val}' -> is_numeric: {is_numeric}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error examining Excel: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    examine_excel()
