#!/usr/bin/env python3
"""
Test the fixed integration module
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_fixed_integration():
    """Test the fixed integration module"""
    print("🧪 Testing Fixed Integration Module")
    print("=" * 40)
    
    try:
        # Import the integration function
        from agent.australia_excel_integration import integrate_australia_excel_data_for_unit
        
        # Mock unit data
        mock_unit_data = {
            "sk": "unit#coal#1#plant#1",
            "unit_number": "1",
            "plant_id": "1",
            "capacity": "660",
            "capacity_unit": "MW",
            "technology": "Super Critical"
        }
        
        print("📋 Testing integration with Bayswater Power Station")
        print()
        
        # Apply integration
        result = integrate_australia_excel_data_for_unit(
            unit_info=mock_unit_data,
            plant_name="Bayswater Power Station",
            country="Australia",
            session_id="test_fixed"
        )
        
        # Check results
        if "australia_excel_integration" in result:
            integration_meta = result["australia_excel_integration"]
            print(f"✅ Integration successful!")
            print(f"   🏢 Excel entity: {integration_meta['excel_entity_name']}")
            print(f"   🏭 Excel plant: {integration_meta['excel_plant_name']}")
            print(f"   📅 Years available: {sorted(integration_meta['years_available'])}")
            print()
            
            # Check gross power generation
            if "gross_power_generation" in result:
                gpg = result["gross_power_generation"]
                print(f"📈 Gross power generation: {len(gpg)} entries")
                for entry in sorted(gpg, key=lambda x: int(x['year'])):
                    print(f"   {entry['year']}: {entry['value']} MWh")
                print()
                
                # Check for all expected years
                years_found = {entry['year'] for entry in gpg}
                expected_years = {'2020', '2021', '2022', '2023', '2024'}
                
                if expected_years.issubset(years_found):
                    print(f"✅ All expected years found in gross power generation: {sorted(expected_years)}")
                else:
                    missing_years = expected_years - years_found
                    print(f"❌ Missing years in gross power generation: {sorted(missing_years)}")
                
                # Specifically check for 2021
                has_2021_gpg = '2021' in years_found
                if has_2021_gpg:
                    gpg_2021 = next(entry for entry in gpg if entry['year'] == '2021')
                    print(f"🎉 2021 gross power generation: {gpg_2021['value']} MWh")
                else:
                    print(f"❌ 2021 gross power generation missing")
            
            # Check emission factor
            if "emission_factor" in result:
                ef = result["emission_factor"]
                print(f"🌿 Emission factor: {len(ef)} entries")
                for entry in sorted(ef, key=lambda x: int(x['year'])):
                    print(f"   {entry['year']}: {entry['value']} tCO2/MWh")
                print()
                
                # Check for all expected years
                years_found = {entry['year'] for entry in ef}
                expected_years = {'2020', '2021', '2022', '2023', '2024'}
                
                if expected_years.issubset(years_found):
                    print(f"✅ All expected years found in emission factor: {sorted(expected_years)}")
                else:
                    missing_years = expected_years - years_found
                    print(f"❌ Missing years in emission factor: {sorted(missing_years)}")
                
                # Specifically check for 2021
                has_2021_ef = '2021' in years_found
                if has_2021_ef:
                    ef_2021 = next(entry for entry in ef if entry['year'] == '2021')
                    print(f"🎉 2021 emission factor: {ef_2021['value']} tCO2/MWh")
                else:
                    print(f"❌ 2021 emission factor missing")
            
            return True
        else:
            print(f"❌ Integration failed - no metadata found")
            print(f"Result keys: {list(result.keys())}")
            return False
            
    except Exception as e:
        print(f"❌ Error during integration test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_integration()
    
    if success:
        print(f"\n🎉 Fixed integration test passed!")
        print(f"✅ The Australia Excel integration is now working correctly.")
        print(f"✅ Both 2021 data and gross power generation are available.")
    else:
        print(f"\n❌ Fixed integration test failed!")
