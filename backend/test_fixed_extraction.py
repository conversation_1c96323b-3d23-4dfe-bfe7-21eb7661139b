#!/usr/bin/env python3
"""
Test the fixed Australia Excel extraction logic
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_fixed_extraction():
    """Test the fixed extraction logic"""
    print("🧪 Testing Fixed Australia Excel Extraction")
    print("=" * 50)
    
    try:
        # Import the integrator
        from agent.australia_excel_integration import AustraliaExcelIntegrator
        
        # Create integrator (this will trigger the new column mapping logic)
        print("🔧 Creating integrator with new column mapping logic...")
        integrator = AustraliaExcelIntegrator()
        
        if not integrator.is_data_available():
            print("❌ Excel data not available")
            return False
        
        print(f"✅ Excel data loaded successfully")
        print()
        
        # Test finding Bayswater data
        print("🔍 Testing Bayswater Power Station data extraction...")
        result = integrator.find_plant_data("Bayswater Power Station")
        
        if result:
            print(f"✅ Found Bayswater data!")
            print(f"   🏢 Entity: {result['entity_name']}")
            print(f"   🏭 Plant: {result['plant_name']}")
            print(f"   📅 Years available: {result['years_available']}")
            print()
            
            # Show gross power generation data
            if result['gross_power_generation']:
                print(f"📈 Gross Power Generation Data:")
                for year, data in sorted(result['gross_power_generation'].items()):
                    print(f"   {year}: {data['value']:,} {data['unit']}")
                print()
            else:
                print("❌ No gross power generation data found")
            
            # Show emission factor data
            if result['emission_factor']:
                print(f"🌿 Emission Factor Data:")
                for year, data in sorted(result['emission_factor'].items()):
                    print(f"   {year}: {data['value']} {data['unit']}")
                print()
            else:
                print("❌ No emission factor data found")
            
            return True
        else:
            print("❌ Could not find Bayswater data")
            return False
            
    except Exception as e:
        print(f"❌ Error during fixed extraction test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unit_level_with_fixed_extraction():
    """Test unit-level integration with the fixed extraction"""
    print("\n🧪 Testing Unit-Level Integration with Fixed Extraction")
    print("=" * 60)
    
    try:
        # Import the unit-level integration function
        from agent.australia_excel_integration import integrate_australia_excel_data_for_unit
        
        # Mock unit data
        mock_unit_data = {
            "sk": "unit#coal#1#plant#1",
            "unit_number": "1",
            "plant_id": "1",
            "capacity": "660",
            "capacity_unit": "MW",
            "technology": "Super Critical",
            
            # Default time-series data (will be replaced)
            "gross_power_generation": [
                {"value": "2500000", "year": "2023"},
                {"value": "2400000", "year": "2022"}
            ],
            "emission_factor": [
                {"value": "0.95", "year": "2023"},
                {"value": "0.97", "year": "2022"}
            ]
        }
        
        print("📋 Testing unit-level integration with Bayswater Power Station")
        print(f"   🔧 Original gross power generation: {len(mock_unit_data['gross_power_generation'])} entries")
        print(f"   🔧 Original emission factor: {len(mock_unit_data['emission_factor'])} entries")
        print()
        
        # Apply integration
        result = integrate_australia_excel_data_for_unit(
            unit_info=mock_unit_data,
            plant_name="Bayswater Power Station",
            country="Australia",
            session_id="test_fixed"
        )
        
        # Check results
        if "australia_excel_integration" in result:
            integration_meta = result["australia_excel_integration"]
            print(f"✅ Integration successful!")
            print(f"   🏢 Excel entity: {integration_meta['excel_entity_name']}")
            print(f"   🏭 Excel plant: {integration_meta['excel_plant_name']}")
            print(f"   📅 Years available: {integration_meta['years_available']}")
            print()
            
            # Check gross power generation
            if "gross_power_generation" in result:
                gpg = result["gross_power_generation"]
                print(f"📈 Updated gross power generation: {len(gpg)} entries")
                for entry in sorted(gpg, key=lambda x: int(x['year'])):
                    print(f"   {entry['year']}: {entry['value']} MWh")
                print()
            
            # Check emission factor
            if "emission_factor" in result:
                ef = result["emission_factor"]
                print(f"🌿 Updated emission factor: {len(ef)} entries")
                for entry in sorted(ef, key=lambda x: int(x['year'])):
                    print(f"   {entry['year']}: {entry['value']} tCO2/MWh")
                print()
            
            return True
        else:
            print(f"❌ Integration failed - no metadata found")
            return False
            
    except Exception as e:
        print(f"❌ Error during unit-level integration test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_fixed_extraction()
    success2 = test_unit_level_with_fixed_extraction()
    
    if success1 and success2:
        print(f"\n🎉 All tests passed! Fixed extraction is working correctly.")
    else:
        print(f"\n❌ Some tests failed!")
