#!/usr/bin/env python3
"""
Test script for Australia Excel integration

This script tests the Australia Excel integration functionality
to ensure it correctly extracts and integrates data from the Excel file.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Simple test without importing the full pipeline
def test_excel_direct():
    """Test Excel file loading directly with pandas"""
    print("=== Testing Excel File Direct Access ===")

    try:
        import pandas as pd

        # Read the Excel file directly
        df = pd.read_excel('Australia_details.xlsx')

        print(f"✅ Excel file loaded successfully")
        print(f"📊 Shape: {df.shape}")
        print(f"📋 Columns: {list(df.columns)}")

        # Show first few rows
        print(f"\n📋 First few rows:")
        print(df.head())

        # Test plant matching logic
        print(f"\n🔍 Testing plant matching...")

        # Look for Bayswater Power Station
        for idx, row in df.iterrows():
            if idx == 0:  # Skip header row
                continue
            plant_name = str(row['Plant Name']).lower()
            if 'bayswater' in plant_name:
                print(f"   ✅ Found Bayswater: {row['Plant Name']}")
                print(f"   📈 2020 generation: {row[2020]}")
                print(f"   🌿 2020 emission: {row['Unnamed: 3']}")
                break

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_integration_class():
    """Test the integration class without full imports"""
    print("\n=== Testing Integration Class ===")

    try:
        # Import only the integration module
        from agent.australia_excel_integration import AustraliaExcelIntegrator

        integrator = AustraliaExcelIntegrator()

        if not integrator.is_data_available():
            print("❌ Excel data not available")
            return False

        print(f"✅ Integration class loaded successfully")
        print(f"📊 Number of plants: {len(integrator.df)}")

        # Test plant matching
        result = integrator.find_plant_data("Bayswater Power Station", "AGL ENERGY LIMITED")

        if result:
            print(f"✅ Plant matching works!")
            print(f"   Plant: {result['plant_name']}")
            print(f"   Entity: {result['entity_name']}")
            print(f"   Years available: {result['years_available']}")

            if result['gross_power_generation']:
                print(f"   📈 Gross power generation data available for {len(result['gross_power_generation'])} years")
                for year, data in list(result['gross_power_generation'].items())[:3]:
                    print(f"      {year}: {data['value']} {data['unit']}")

            if result['emission_factor']:
                print(f"   🌿 Emission factor data available for {len(result['emission_factor'])} years")
                for year, data in list(result['emission_factor'].items())[:3]:
                    print(f"      {year}: {data['value']} {data['unit']}")
        else:
            print("❌ Plant matching failed")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False



def main():
    """Run all tests"""
    print("🧪 Australia Excel Integration Test Suite")
    print("=" * 50)

    try:
        # Test 1: Direct Excel access
        if not test_excel_direct():
            print("❌ Direct Excel test failed - stopping tests")
            return

        # Test 2: Integration class
        if not test_integration_class():
            print("❌ Integration class test failed")
            return

        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")

    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
