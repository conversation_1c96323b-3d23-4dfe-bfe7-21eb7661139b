#!/usr/bin/env python3
"""
Analyze the Australia Excel file structure to understand data extraction issues
"""

import pandas as pd
import numpy as np

def analyze_excel_structure():
    """Analyze the Excel file structure in detail"""
    print("🔍 ANALYZING AUSTRALIA EXCEL FILE STRUCTURE")
    print("=" * 60)
    
    try:
        # Load the Excel file
        df = pd.read_excel('Australia_details.xlsx')
        print(f"✅ Excel file loaded successfully")
        print(f"📊 Shape: {df.shape}")
        print()
        
        # Show column structure
        print("📋 COLUMN STRUCTURE:")
        for i, col in enumerate(df.columns):
            col_type = type(col).__name__
            print(f"  Column {i:2d}: '{col}' (type: {col_type})")
        print()
        
        # Show first few rows
        print("📄 FIRST 5 ROWS (RAW DATA):")
        print(df.head())
        print()
        
        # Remove header row and analyze
        df_data = df.iloc[1:].reset_index(drop=True)
        print("📄 DATA ROWS (AFTER REMOVING HEADER):")
        print(df_data.head())
        print()
        
        # Analyze year columns
        print("📅 YEAR COLUMN ANALYSIS:")
        year_columns = {}
        emission_columns = {}
        
        for i, col in enumerate(df.columns):
            if isinstance(col, int) and 2020 <= col <= 2024:
                year_columns[col] = i
                print(f"  Year {col}: Column {i}")
                
                # Check next column for emission factor
                if i + 1 < len(df.columns):
                    next_col = df.columns[i + 1]
                    emission_columns[col] = i + 1
                    print(f"    → Emission factor column {i+1}: '{next_col}'")
        print()
        
        # Analyze Bayswater data specifically
        print("🏭 BAYSWATER POWER STATION ANALYSIS:")
        bayswater_mask = df_data['Plant Name'].str.contains('Bayswater', case=False, na=False)
        bayswater_rows = df_data[bayswater_mask]
        
        if not bayswater_rows.empty:
            print(f"✅ Found {len(bayswater_rows)} Bayswater row(s)")
            row = bayswater_rows.iloc[0]
            
            print(f"  🏢 Entity: {row['Entity Name']}")
            print(f"  🏭 Plant: {row['Plant Name']}")
            print()
            
            print("  📊 YEAR-WISE DATA EXTRACTION:")
            for year in sorted(year_columns.keys()):
                gross_col_idx = year_columns[year]
                gross_value = row.iloc[gross_col_idx]
                
                print(f"    Year {year}:")
                print(f"      Column {gross_col_idx}: {gross_value} (type: {type(gross_value).__name__})")
                print(f"      Is NaN: {pd.isna(gross_value)}")
                print(f"      Is Zero: {gross_value == 0 if not pd.isna(gross_value) else 'N/A'}")
                
                # Check emission factor
                if year in emission_columns:
                    emission_col_idx = emission_columns[year]
                    emission_value = row.iloc[emission_col_idx]
                    print(f"      Emission Column {emission_col_idx}: {emission_value} (type: {type(emission_value).__name__})")
                    print(f"      Emission Is NaN: {pd.isna(emission_value)}")
                print()
        else:
            print("❌ No Bayswater rows found")
        
        # Check for 2021 specifically
        print("🔍 2021 DATA INVESTIGATION:")
        if 2021 in year_columns:
            col_idx = year_columns[2021]
            print(f"  2021 is in year_columns at column {col_idx}")
            
            # Check all plants for 2021 data
            non_null_2021 = df_data.iloc[:, col_idx].dropna()
            print(f"  Non-null 2021 values: {len(non_null_2021)}")
            if len(non_null_2021) > 0:
                print(f"  Sample 2021 values: {non_null_2021.head().tolist()}")
        else:
            print("  ❌ 2021 not found in year_columns")
            print(f"  Available years: {list(year_columns.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing Excel file: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_current_extraction_logic():
    """Test the current extraction logic to see what's failing"""
    print("\n🧪 TESTING CURRENT EXTRACTION LOGIC")
    print("=" * 50)
    
    try:
        df = pd.read_excel('Australia_details.xlsx')
        df = df.iloc[1:].reset_index(drop=True)  # Remove header
        
        # Find Bayswater
        bayswater_mask = df['Plant Name'].str.contains('Bayswater', case=False, na=False)
        if not bayswater_mask.any():
            print("❌ Bayswater not found")
            return False
        
        row = df[bayswater_mask].iloc[0]
        
        # Test year column mapping (current logic)
        year_columns = {}
        emission_columns = {}
        
        for i, col in enumerate(df.columns):
            if isinstance(col, int) and 2020 <= col <= 2024:
                year_columns[col] = i
                if i + 1 < len(df.columns):
                    emission_columns[col] = i + 1
        
        print(f"Year columns found: {year_columns}")
        print(f"Emission columns: {emission_columns}")
        print()
        
        # Extract data using current logic
        gross_power_data = {}
        emission_factor_data = {}
        
        for year in year_columns.keys():
            try:
                # Get gross power generation
                gross_col_idx = year_columns[year]
                gross_value = row.iloc[gross_col_idx]
                
                print(f"Year {year}:")
                print(f"  Gross power (col {gross_col_idx}): {gross_value}")
                
                if pd.notna(gross_value) and gross_value != 0:
                    gross_power_data[str(year)] = {
                        "value": float(gross_value),
                        "unit": "MWh"
                    }
                    print(f"  ✅ Gross power extracted: {gross_value}")
                else:
                    print(f"  ❌ Gross power skipped (NaN or zero)")
                
                # Get emission factor
                if year in emission_columns:
                    emission_col_idx = emission_columns[year]
                    emission_value = row.iloc[emission_col_idx]
                    
                    print(f"  Emission factor (col {emission_col_idx}): {emission_value}")
                    
                    if pd.notna(emission_value) and emission_value != 0:
                        emission_factor_data[str(year)] = {
                            "value": float(emission_value),
                            "unit": "tCO2/MWh"
                        }
                        print(f"  ✅ Emission factor extracted: {emission_value}")
                    else:
                        print(f"  ❌ Emission factor skipped (NaN or zero)")
                print()
                        
            except (ValueError, IndexError) as e:
                print(f"  ❌ Error extracting data for year {year}: {e}")
        
        print(f"📈 Final gross power data: {gross_power_data}")
        print(f"🌿 Final emission factor data: {emission_factor_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing extraction logic: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = analyze_excel_structure()
    success2 = test_current_extraction_logic()
    
    if success1 and success2:
        print(f"\n🎉 Analysis completed successfully!")
    else:
        print(f"\n❌ Analysis failed!")
