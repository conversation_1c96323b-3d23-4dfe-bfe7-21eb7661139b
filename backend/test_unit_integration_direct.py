#!/usr/bin/env python3
"""
Direct test of unit-level Australia Excel integration
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_unit_integration_direct():
    """Test the unit integration function directly"""
    print("🧪 Direct Unit-Level Australia Excel Integration Test")
    print("=" * 55)
    
    try:
        # Import the integration function
        from agent.australia_excel_integration import integrate_australia_excel_data_for_unit
        
        # Mock unit data
        mock_unit_data = {
            "sk": "unit#coal#1#plant#1",
            "unit_number": "1",
            "plant_id": "1",
            "capacity": "660",
            "capacity_unit": "MW",
            "technology": "Super Critical",
            
            # Default time-series data (will be replaced)
            "gross_power_generation": [
                {"value": "2500000", "year": "2023"},
                {"value": "2400000", "year": "2022"}
            ],
            "emission_factor": [
                {"value": "0.95", "year": "2023"},
                {"value": "0.97", "year": "2022"}
            ]
        }
        
        print("📋 Testing with Bayswater Power Station (Australian coal plant)")
        print(f"   🔧 Original gross power generation: {len(mock_unit_data['gross_power_generation'])} entries")
        print(f"   🔧 Original emission factor: {len(mock_unit_data['emission_factor'])} entries")
        
        # Apply integration
        result = integrate_australia_excel_data_for_unit(
            unit_info=mock_unit_data,
            plant_name="Bayswater Power Station",
            country="Australia",
            session_id="test_direct"
        )
        
        # Check results
        if "australia_excel_integration" in result:
            integration_meta = result["australia_excel_integration"]
            print(f"✅ Integration successful!")
            print(f"   🏢 Excel entity: {integration_meta['excel_entity_name']}")
            print(f"   🏭 Excel plant: {integration_meta['excel_plant_name']}")
            print(f"   📅 Years available: {integration_meta['years_available']}")
            
            # Check gross power generation
            if "gross_power_generation" in result:
                gpg = result["gross_power_generation"]
                print(f"   📈 Updated gross power generation: {len(gpg)} entries")
                for entry in gpg[:3]:  # Show first 3
                    print(f"      {entry['year']}: {entry['value']} MWh")
            
            # Check emission factor
            if "emission_factor" in result:
                ef = result["emission_factor"]
                print(f"   🌿 Updated emission factor: {len(ef)} entries")
                for entry in ef[:3]:  # Show first 3
                    print(f"      {entry['year']}: {entry['value']} tCO2/MWh")
            
            return True
        else:
            print(f"❌ Integration failed - no metadata found")
            return False
            
    except Exception as e:
        print(f"❌ Error during direct integration test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_unit_integration_direct()
    if success:
        print(f"\n🎉 Direct unit integration test passed!")
    else:
        print(f"\n❌ Direct unit integration test failed!")
