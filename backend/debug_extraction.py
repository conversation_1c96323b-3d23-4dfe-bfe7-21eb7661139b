#!/usr/bin/env python3
"""
Debug the data extraction process to see why gross power generation is not being extracted
"""

import sys
import os
from pathlib import Path
import pandas as pd

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_extraction():
    """Debug the data extraction process"""
    print("🔍 Debugging Data Extraction Process")
    print("=" * 50)
    
    try:
        from agent.australia_excel_integration import AustraliaExcelIntegrator
        
        # Create integrator
        integrator = AustraliaExcelIntegrator()
        
        print(f"📊 Year columns: {integrator.year_columns}")
        print(f"📊 Emission columns: {integrator.emission_columns}")
        print()
        
        # Find Bayswater Power Station data
        plant_data = integrator.find_plant_data("Bayswater Power Station")
        
        if plant_data:
            print(f"✅ Found plant data for Bayswater Power Station")
            print(f"   Entity: {plant_data['entity_name']}")
            print(f"   Plant: {plant_data['plant_name']}")
            print(f"   Years available: {plant_data['years_available']}")
            print()
            
            print(f"📈 Gross Power Generation:")
            gpg = plant_data.get('gross_power_generation', {})
            print(f"   Data: {gpg}")
            if gpg:
                for year, data in sorted(gpg.items()):
                    print(f"   {year}: {data['value']} {data['unit']}")
            else:
                print(f"   ❌ No gross power generation data found!")
            print()
            
            print(f"🌿 Emission Factor:")
            ef = plant_data.get('emission_factor', {})
            print(f"   Data: {ef}")
            if ef:
                for year, data in sorted(ef.items()):
                    print(f"   {year}: {data['value']} {data['unit']}")
            else:
                print(f"   ❌ No emission factor data found!")
            print()
            
            # Let's manually debug the extraction process
            print(f"🔧 Manual Debug of Extraction Process:")
            print(f"=" * 40)
            
            # Find the row for Bayswater
            for idx, row in integrator.df.iterrows():
                if "bayswater" in str(row['Plant Name']).lower():
                    print(f"📋 Found Bayswater row at index {idx}")
                    print(f"   Entity: {row['Entity Name']}")
                    print(f"   Plant: {row['Plant Name']}")
                    print()
                    
                    # Check each year column
                    for year in integrator.year_columns.keys():
                        gross_col_idx = integrator.year_columns[year]
                        gross_value = row.iloc[gross_col_idx]
                        
                        print(f"   Year {year}:")
                        print(f"     Gross column index: {gross_col_idx}")
                        print(f"     Raw gross value: '{gross_value}' (type: {type(gross_value).__name__})")
                        print(f"     Is NaN: {pd.isna(gross_value)}")
                        print(f"     Is zero: {gross_value == 0}")
                        print(f"     Condition check: pd.notna({gross_value}) and {gross_value} != 0 = {pd.notna(gross_value) and gross_value != 0}")
                        
                        if year in integrator.emission_columns:
                            emission_col_idx = integrator.emission_columns[year]
                            emission_value = row.iloc[emission_col_idx]
                            print(f"     Emission column index: {emission_col_idx}")
                            print(f"     Raw emission value: '{emission_value}' (type: {type(emission_value).__name__})")
                            print(f"     Emission condition check: pd.notna({emission_value}) and {emission_value} != 0 = {pd.notna(emission_value) and emission_value != 0}")
                        print()
                    
                    break
        else:
            print(f"❌ No plant data found for Bayswater Power Station")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_extraction()
