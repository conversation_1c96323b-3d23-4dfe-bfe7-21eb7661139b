"""
Debug script to check Jhajjar Power Plant information
"""

from database_manager import get_database_manager

def main():
    print("🔍 DEBUGGING JHAJJAR POWER PLANT")
    print("=" * 50)
    
    db_manager = get_database_manager()
    
    # Check what's in the database for Jhajjar
    plant_info = db_manager.check_plant_exists("Jhajjar Power Plant")
    
    if plant_info:
        print("✅ Found in database:")
        print(f"   Plant Name: {plant_info['plant_name']}")
        print(f"   Organization: {plant_info['org_name']}")
        print(f"   Country: {plant_info['country']}")
        print(f"   UID: {plant_info['org_id']}")
        print(f"   Status: {plant_info['plant_status']}")
        print(f"   Created: {plant_info.get('created_at', 'N/A')}")
    else:
        print("❌ Not found in database")
    
    # Check all Jindal plants
    print("\n🏢 ALL JINDAL POWER LIMITED PLANTS:")
    jindal_plants = db_manager.get_plants_by_organization("Jindal Power Limited")
    for plant in jindal_plants:
        print(f"   • {plant['plant_name']} (UID: {plant['org_id']})")

if __name__ == "__main__":
    main()