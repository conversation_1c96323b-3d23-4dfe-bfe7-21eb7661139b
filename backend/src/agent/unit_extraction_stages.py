"""
Multi-stage unit data extraction for better API call efficiency and data quality
"""

import os
import json
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

def parse_json_response(content: str, stage_name: str) -> dict:
    """Enhanced JSON parsing with better error handling and logging"""
    print(f"🔍 {stage_name} raw response length: {len(content)} chars")
    print(f"🔍 Response preview: {content[:200]}...")
    
    # Method 1: Try to extract JSON from the response using brackets
    start_idx = content.find('{')
    end_idx = content.rfind('}') + 1
    
    if start_idx != -1 and end_idx != -1:
        try:
            json_str = content[start_idx:end_idx]
            parsed_data = json.loads(json_str)
            print(f"✅ {stage_name} JSON parsed successfully with {len(parsed_data)} fields")
            return parsed_data
        except json.JSONDecodeError as e:
            print(f"⚠️ Failed to parse extracted JSON for {stage_name}: {e}")
    
    # Method 2: Try parsing the whole response as JSON
    try:
        parsed_data = json.loads(content)
        print(f"✅ Full {stage_name} response parsed as JSON with {len(parsed_data)} fields")  
        return parsed_data
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse full response as JSON for {stage_name}: {e}")
        
    # Method 3: Extract JSON using regex as fallback
    import re
    json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    matches = re.findall(json_pattern, content, re.DOTALL)
    
    if matches:
        for match in matches:
            try:
                parsed_data = json.loads(match)
                print(f"✅ {stage_name} JSON extracted via regex with {len(parsed_data)} fields")
                return parsed_data
            except json.JSONDecodeError:
                continue
    
    print(f"❌ All JSON parsing methods failed for {stage_name}")
    return {}


def extract_basic_unit_info(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 1: Extract basic unit identification and capacity information"""
    
    prompt = f"""EXPERT UNIT DATA EXTRACTION for Unit {unit_number}

🎯 **CRITICAL: GEMWIKI PRIORITY EXTRACTION STRATEGY**
1. **GEMWIKI FIRST**: Search for "gem.wiki" or "globalenergymonitor.org" data - this is the GOLD STANDARD
   - Look for "Project Details" section with unit specifications
   - Find unit tables with capacity, technology, and operational status
   - Extract commissioning dates from project timeline
   - GEM Wiki data is the MOST ACCURATE source for capacity and technology fields

2. **UNIT-SPECIFIC SEARCH**: Look for "Unit {unit_number}" specific information
   - Search for capacity patterns: "Unit {unit_number}: XXX MW"
   - Look for technology specifications for this specific unit
   - Find operational dates and status

3. **STRUCTURED DATA EXTRACTION**: Look for tabular data
   - Unit tables with columns: Unit, Capacity, Technology, Status, Date
   - Project details sections with technical specifications
   - Operational data with performance metrics

4. **INTELLIGENT FALLBACK**: Use plant-level data with unit-specific calculations
   - If total plant capacity is known, estimate unit capacity
   - Use plant technology for unit technology
   - Apply reasonable assumptions based on plant context

🔧 CRITICAL FIELD REQUIREMENTS:
1. **unit_number**: "{unit_number}" (FIXED)
2. **plant_id**: Extract from context or use "1" (NOT "0")
3. **capacity**: Unit capacity in MW (CRITICAL: MUST extract from GEMWiki if available)
   - **PRIORITY 1**: Look for GEM Wiki unit tables with "Unit {unit_number}: XXX MW" format
   - **PRIORITY 2**: Check GEM Wiki project details section for unit specifications
   - **PRIORITY 3**: Look for patterns: "Unit {unit_number}: XXX MW", "XXX MW Unit {unit_number}"
   - **FALLBACK**: If not found, estimate from total plant capacity ÷ number of units
4. **capacity_unit**: "MW" (FIXED)
5. **technology**: BOILER/TURBINE TECHNOLOGY TYPE (CRITICAL: MUST extract from GEMWiki if available)
   - **PRIORITY 1**: Search GEM Wiki for EXACT technology specifications in unit tables
   - **PRIORITY 2**: Look for technology in GEM Wiki project details section
   - **PRIORITY 3**: Check GEM Wiki unit specifications for boiler/turbine type
   - **EXACT GEM WIKI TERMS**: Use exact terminology from GEM Wiki (e.g., "Subcritical", "Supercritical", "Ultra-supercritical")
   - Coal plants: "Sub-Critical", "Super-Critical", "Ultra-Super-Critical", "Subcritical", "Supercritical"
   - Gas plants: "Combined Cycle Gas Turbine (CCGT)", "Open Cycle Gas Turbine (OCGT)"
   - Steam plants: "Pulverized Coal", "Circulating Fluidized Bed"
   - IMPORTANT: This is NOT fuel type (Coal/Gas) but the actual technology used
6. **boiler_type**: Boiler specification (extract from technical details)
7. **commencement_date**: Operation start date in YYYY-MM-DD format
   - Look for "commissioned", "operational", "started commercial operation"
   - Extract year at minimum, add "-01-01" if only year available

🏭 PLANT CONTEXT:
- Plant Name: {plant_context.get('plant_name', 'Unknown')}
- Plant Technology: {plant_context.get('plant_technology', 'Unknown')}
- Plant Capacity: {plant_context.get('capacity', 'Unknown')} MW
- Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

INSTRUCTIONS:
🎯 **CRITICAL PRIORITY: GEMWiki (gem.wiki) data for capacity and technology - this is the MOST RELIABLE source**
- **STEP 1**: Search for GEM Wiki content first (gem.wiki, globalenergymonitor.org)
- **STEP 2**: Look for unit tables, specifications, and technical details in GEMWiki content
- **STEP 3**: Extract exact capacity (MW) and technology type from GEM Wiki unit specifications
- **STEP 4**: If GEM Wiki data unavailable, use other sources as fallback
- If unit-specific capacity not found, divide total plant capacity by number of units
- Use "Not available" only if no relevant information exists in any source
- Provide your best estimate based on available data, prioritizing GEM Wiki accuracy

Respond ONLY with JSON format:
{{
  "unit_number": "{unit_number}",
  "plant_id": "extracted_or_0",
  "capacity": "estimated_capacity_value",
  "capacity_unit": "MW",
  "technology": "extracted_technology",
  "boiler_type": "extracted_or_Not_available",
  "commencement_date": "extracted_date_or_Not_available"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Basic Info")
        if parsed_data and len(parsed_data) > 0:  # Check if dict has content
            return parsed_data
            
    except Exception as e:
        print(f"❌ Basic info extraction failed: {e}")
        return {
            "unit_number": unit_number,
            "plant_id": "0",
            "capacity": "Not available", 
            "capacity_unit": "MW",
            "technology": "Not available",
            "boiler_type": "Not available",
            "commencement_date": "Not available"
        }


def extract_performance_metrics(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 2: Extract performance and operational metrics with SPECIFIC FIELD TARGETS"""

    prompt = f"""COMPREHENSIVE PERFORMANCE DATA EXTRACTION for Unit {unit_number}

🎯 MULTI-SOURCE PERFORMANCE EXTRACTION STRATEGY:
1. **GEMWIKI PERFORMANCE DATA**: Look for operational statistics and performance metrics
2. **REGULATORY REPORTS**: Search for annual performance reports and filings
3. **INDUSTRY DATABASES**: Check for capacity factors and generation data
4. **TECHNICAL SPECIFICATIONS**: Extract design parameters and efficiency ratings

🔧 CRITICAL PERFORMANCE FIELDS (MUST EXTRACT):

**EFFICIENCY & HEAT RATE:**
- heat_rate: Heat rate in kCal/kWh (search for efficiency data, typical ranges: Coal: 2000-2500, Gas: 1800-2200)
- heat_rate_unit: "Kcal/kWh" (FIXED)
- coal_unit_efficiency: Unit efficiency percentage (search for "efficiency", "thermal efficiency", typical: 35-45%)

**OPERATIONAL PERFORMANCE (TIME SERIES):**
- plf: Plant Load Factor by year (search for "capacity factor", "load factor", "utilization")
  * Look for patterns: "PLF: XX%", "Capacity Factor: XX%", "Load Factor: XX%"
  * Extract multiple years if available: 2020, 2021, 2022, 2023
- PAF: Plant Availability Factor by year (search for "availability factor", "availability")
  * Look for "Available hours", "Availability: XX%", "Plant availability"
- auxiliary_power_consumed: Auxiliary power consumption (search for "auxiliary consumption", "station use")
  * Look for "Auxiliary power: XX%", "Station consumption: XX MWh"
- gross_power_generation: Gross generation by year (search for "generation", "output", "produced")
  * Look for "Generated: XX GWh", "Output: XX MWh", "Production: XX units"

**LIFETIME & DESIGN:**
- unit_lifetime: Design lifetime in years (search for "design life", "operational life", typical: 25-40 years)
- remaining_useful_life: Remaining years (calculate from commissioning date if available)

🔍 SEARCH PATTERNS TO LOOK FOR:
- "Unit {unit_number} performance", "Unit {unit_number} statistics"
- "Capacity factor", "Load factor", "PLF", "Availability factor"
- "Heat rate", "Efficiency", "Thermal efficiency"
- "Generation data", "Output statistics", "Performance metrics"
- "Auxiliary consumption", "Station use", "Parasitic load"

🏭 PLANT CONTEXT FOR REFERENCE:
- Plant: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
- Technology: {plant_context.get('plant_technology', 'Unknown')}
- Capacity: {plant_context.get('capacity', 'Unknown')} MW
- Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format. Use empty arrays [] for missing time-series data.
Example:
{{
  "heat_rate": "2150",
  "heat_rate_unit": "Kcal/kWh",
  "plf": [{{"value": "75.5", "year": "2023"}}, {{"value": "78.2", "year": "2022"}}],
  "PAF": [{{"value": "85.3", "year": "2023"}}],
  "auxiliary_power_consumed": [],
  "gross_power_generation": [{{"value": "3500", "year": "2023"}}],
  "coal_unit_efficiency": "42.5",
  "unit_lifetime": "25",
  "remaining_useful_life": "15"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Performance Metrics")
        if parsed_data and len(parsed_data) > 0:  # Check if dict has content
            return parsed_data
            
    except Exception as e:
        print(f"❌ Performance metrics extraction failed: {e}")
        return {
            "heat_rate": "Not available",
            "heat_rate_unit": "Kcal/kWh",  # Default unit
            "plf": [],
            "PAF": [],
            "auxiliary_power_consumed": [],
            "gross_power_generation": [],
            "coal_unit_efficiency": "Not available",
            "unit_lifetime": "Not available",
            "remaining_useful_life": "Not available"
        }


def extract_fuel_and_emissions(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 3: Extract fuel composition and emission data with SPECIFIC FIELD TARGETS"""

    prompt = f"""Extract ONLY the fuel composition and emission information for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. annual_operational_hours: Annual operational hours (FIXED VALUE: 8760)
2. blending_percentage_of_biomass: Biomass co-firing blending percentage (FIXED VALUE: 0.15)
3. emission_factor_coal: Emission factor for coal in kg CO₂e/kg of fuel
4. emission_factor_gas: Emission factor for natural gas (FIXED VALUE: 2.69)
5. emission_factor_of_gas_unit: Unit for gas emission factor (kg CO₂e/kg)
6. emission_factor_unit: Unit for emission factor (kgCO₂/kWh)
7. fgds_status: Status of Flue Gas Desulfurization System

COAL TYPE AND EMISSION FACTOR RESEARCH:
- What is the primary type of coal (bituminous, sub-bituminous, lignite, anthracite) used at Unit {unit_number}?
- What is the typical emission factor for that coal type in kg CO₂e/kg of coal burned?
- Source of emission factor data (IPCC, IEA, national inventory guidelines)?

FGDS SYSTEM RESEARCH:
- Does Unit {unit_number} have FGDS installed and operational?
- Type of FGDS (wet limestone, dry FGD, seawater FGD)?
- Year of FGDS commissioning?

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format:
{{
  "annual_operational_hours": 8760,
  "blending_percentage_of_biomass": 0.15,
  "emission_factor_coal": "emission_factor_value_with_source",
  "emission_factor_gas": 2.69,
  "emission_factor_of_gas_unit": "kg CO2e/kg",
  "emission_factor_unit": "kgCO_2/kWH",
  "fgds_status": "Fully_installed_operational_or_status",
  "fuel_type": [
    {{"fuel": "Coal", "type": "Bituminous", "years_percentage": {{"2023": "80", "2022": "85"}}}},
    {{"fuel": "Biomass", "type": "Wood chips", "years_percentage": {{"2023": "20", "2022": "15"}}}}
  ],
  "emission_factor": [{{"value": "0.92", "year": "2023"}}],
  "gcv_coal": "5500",
  "gcv_coal_unit": "kCal/kg",
  "gcv_biomass": "4200",
  "gcv_biomass_unit": "kCal/kg",
  "efficiency_loss_biomass_cofiring": "2.5"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Fuel and emissions extraction failed: {e}")
        return {
            "annual_operational_hours": 8760,  # FIXED VALUE
            "blending_percentage_of_biomass": 0.15,  # FIXED VALUE
            "emission_factor_coal": "Not available",
            "emission_factor_gas": 2.69,  # FIXED VALUE
            "emission_factor_of_gas_unit": "kgCO2e/kg of fuel",  # Fixed value
            "emission_factor_unit": "kgCO_2/kWH",  # Default value
            "fgds_status": "Not available",
            "fuel_type": [],
            "emission_factor": [],
            "gcv_coal": "Not available",
            "gcv_coal_unit": "kCal/kg",
            "gcv_biomass": "Not available",
            "gcv_biomass_unit": "kCal/kg",
            "efficiency_loss_biomass_cofiring": "Not available"
        }


def get_country_currency(country: str) -> str:
    """Get the currency code for a country"""
    currency_map = {
        'United States': 'USD',
        'USA': 'USD',
        'US': 'USD',
        'India': 'INR',
        'China': 'CNY',
        'Japan': 'JPY',
        'Germany': 'EUR',
        'United Kingdom': 'GBP',
        'UK': 'GBP',
        'Canada': 'CAD',
        'Australia': 'AUD',
        'Brazil': 'BRL',
        'South Africa': 'ZAR',
        'Indonesia': 'IDR',
        'Thailand': 'THB',
        'Malaysia': 'MYR',
        'Philippines': 'PHP',
        'Vietnam': 'VND',
        'South Korea': 'KRW',
        'Taiwan': 'TWD',
        'Poland': 'PLN',
        'Turkey': 'TRY',
        'Mexico': 'MXN',
        'Chile': 'CLP',
        'Colombia': 'COP',
        'Argentina': 'ARS',
        # CRITICAL FIX: Add missing European countries
        'Italy': 'EUR',
        'France': 'EUR',
        'Spain': 'EUR',
        'Netherlands': 'EUR',
        'Belgium': 'EUR',
        'Austria': 'EUR',
        'Portugal': 'EUR',
        'Greece': 'EUR',
        'Ireland': 'EUR',
        'Finland': 'EUR',
        'Luxembourg': 'EUR',
        'Slovenia': 'EUR',
        'Slovakia': 'EUR',
        'Estonia': 'EUR',
        'Latvia': 'EUR',
        'Lithuania': 'EUR',
        'Malta': 'EUR',
        'Cyprus': 'EUR',
        # Other major countries
        'Switzerland': 'CHF',
        'Norway': 'NOK',
        'Sweden': 'SEK',
        'Denmark': 'DKK',
        'Czech Republic': 'CZK',
        'Hungary': 'HUF',
        'Romania': 'RON',
        'Bulgaria': 'BGN',
        'Croatia': 'HRK',
        'Russia': 'RUB',
        'Ukraine': 'UAH',
        'Israel': 'ILS',
        'Saudi Arabia': 'SAR',
        'UAE': 'AED',
        'Egypt': 'EGP',
        'Nigeria': 'NGN',
        'Kenya': 'KES',
        'Morocco': 'MAD',
        'Algeria': 'DZD',
        'Tunisia': 'TND',
        # CRITICAL FIX: Add missing African countries
        'Zambia': 'ZMW',
        'Zimbabwe': 'ZWL',
        'Botswana': 'BWP',
        'Namibia': 'NAD',
        'Ghana': 'GHS',
        'Tanzania': 'TZS',
        'Uganda': 'UGX',
        'Rwanda': 'RWF',
        'Ethiopia': 'ETB',
        'Mozambique': 'MZN',
        'Angola': 'AOA',
        'Cameroon': 'XAF',
        'Ivory Coast': 'XOF',
        'Senegal': 'XOF',
        'Mali': 'XOF',
        'Burkina Faso': 'XOF',
        'Niger': 'XOF',
        'Chad': 'XAF',
        'Central African Republic': 'XAF',
        'Democratic Republic of Congo': 'CDF',
        'Republic of Congo': 'XAF'
    }

    # Try exact match first
    if country in currency_map:
        return currency_map[country]

    # Try partial match
    country_lower = country.lower()
    for country_key, currency in currency_map.items():
        if country_key.lower() in country_lower or country_lower in country_key.lower():
            return currency

    # Default to USD if country not found
    return 'USD'

def extract_economic_data(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 4: Extract economic and conversion cost data with SPECIFIC FIELD TARGETS"""

    country = plant_context.get('country', 'Unknown')
    currency = get_country_currency(country)
    prompt = f"""Extract ONLY the economic and conversion cost information for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. capex_required_retrofit_biomass: CAPEX for biomass cofiring retrofit using Palm Kernel Shells (PKS)
2. capex_required_retrofit_biomass_unit: Unit for retrofit CAPEX (FIXED VALUE: {currency}/MW)
3. capex_required_renovation_open_cycle: CAPEX for Open Cycle Gas Turbine (OCGT) conversion
4. capex_required_renovation_open_cycle_unit: Unit for OCGT CAPEX (FIXED VALUE: {currency}/MW)
5. capex_required_renovation_closed_cycle: CAPEX for Combined Cycle Gas Turbine (CCGT) conversion
6. capex_required_renovation_closed_cycle_unit: Unit for CCGT CAPEX (FIXED VALUE: {currency}/MW)

RETROFIT COST RESEARCH QUESTIONS:
- What is the average cost per megawatt (MW) of retrofitting Unit {unit_number} into a biomass co-firing plant in {country}, using Palm Kernel Shells (PKS)?
- How much would it cost to retrofit Unit {unit_number} into an Open Cycle Gas Turbine (OCGT) power plant in {country}?
- How much would it cost to retrofit Unit {unit_number} into a Combined Cycle Gas Turbine (CCGT) power plant in {country}?

IMPORTANT INSTRUCTIONS:
- Express costs as full numbers in local currency (e.g., 145000000 IDR not 145 million IDR)
- Include year of estimate if available
- Consider plant size, age, and site conditions in your estimates
- Use country-specific data when available

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {country}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "capex_required_retrofit_biomass": "50000000",
  "capex_required_retrofit_biomass_unit": "{currency}/MW",
  "capex_required_renovation_open_cycle": "200000000",
  "capex_required_renovation_open_cycle_unit": "{currency}/MW",
  "capex_required_renovation_closed_cycle": "350000000",
  "capex_required_renovation_closed_cycle_unit": "{currency}/MW"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Economic data extraction failed: {e}")
        return {
            "capex_required_retrofit_biomass": "Not available",
            "capex_required_retrofit_biomass_unit": f"{currency}/MW",  # FIXED VALUE with actual currency
            "capex_required_renovation_open_cycle": "Not available",
            "capex_required_renovation_open_cycle_unit": f"{currency}/MW",  # FIXED VALUE with actual currency
            "capex_required_renovation_closed_cycle": "Not available",
            "capex_required_renovation_closed_cycle_unit": f"{currency}/MW"  # FIXED VALUE with actual currency
        }


def extract_technical_parameters(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 5: Extract country-specific technical parameters"""
    
    prompt = f"""Extract ONLY the country-specific technical parameters for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. gcv_natural_gas: Gross calorific value of natural gas
2. gcv_natural_gas_unit: Unit for gas GCV  
3. open_cycle_gas_turbine_efficency: OCGT efficiency for the country
4. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country
5. combined_cycle_heat_rate: CCGT heat rate for the country
6. open_cycle_heat_rate: OCGT heat rate for the country

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "gcv_natural_gas": "10000",
  "gcv_natural_gas_unit": "kcal/scm",
  "open_cycle_gas_turbine_efficency": "35",
  "closed_cylce_gas_turbine_efficency": "55",
  "combined_cycle_heat_rate": "1800",
  "open_cycle_heat_rate": "2800"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Technical parameters extraction failed: {e}")
        return {
            "gcv_natural_gas": "10000",
            "gcv_natural_gas_unit": "kcal/scm",
            "open_cycle_gas_turbine_efficency": "Not available",
            "closed_cylce_gas_turbine_efficency": "Not available",
            "combined_cycle_heat_rate": "Not available",
            "open_cycle_heat_rate": "Not available"
        }


def combine_unit_data(stage_results: list, unit_number: str, plant_context: dict) -> dict:
    """Combine all stage results into final unit data structure"""
    
    # Start with required base structure
    # SIMPLIFIED FIX: Use plant_type directly from plant_context (already has correct value from plant data)
    plant_type = plant_context.get('plant_type', plant_context.get('plant_technology', 'coal'))

    print(f"🔧 Using plant_type from plant_context: {plant_type}")

    sequential_plant_id = plant_context.get('plant_id', '1')  # Sequential number (1, 2, 3, etc.)
    plant_uid = plant_context.get('plant_uid', plant_context.get('org_id', 'default null'))  # UUID

    print(f"🔧 UNIT DATA GENERATION: plant_type='{plant_type}' (from plant_context), unit_id='{unit_number}', plant_id='{sequential_plant_id}', plant_uid='{plant_uid}'")
    print(f"🔍 DEBUG PLANT CONTEXT: {plant_context}")

    combined_data = {
        "sk": f"unit#{plant_type}#{unit_number}#plant#{sequential_plant_id}",
        "unit_number": unit_number,
        "plant_id": sequential_plant_id,  # Sequential number (1, 2, 3, etc.)
        "plant_uid": plant_uid,  # UUID from database
        # Default values for fields that might be missing

        "pk": plant_uid,  # Use plant_uid (UUID) as primary key

        # FIXED VALUES as specified
        "annual_operational_hours": 8760,
        "blending_percentage_of_biomass": 0.15,
        "emission_factor_gas": 2.69,

        # Currency-specific units
        "capex_required_renovation_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_retrofit_biomass_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_renovation_open_cycle_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_renovation_closed_cycle_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",

        # Mandatory time series fields (NEVER EMPTY - with fallback calculations)
        "plf": [
            {"value": 65.0, "year": "2023"},
            {"value": 62.0, "year": "2022"},
            {"value": 68.0, "year": "2021"}
        ],
        "PAF": [
            {"value": 85.0, "year": "2023"},
            {"value": 82.0, "year": "2022"},
            {"value": 87.0, "year": "2021"}
        ],
        "auxiliary_power_consumed": [
            {"value": 8.5, "year": "2023"},
            {"value": 8.2, "year": "2022"},
            {"value": 8.8, "year": "2021"}
        ],
        "gross_power_generation": [
            {"value": 2500.0, "year": "2023"},
            {"value": 2400.0, "year": "2022"},
            {"value": 2600.0, "year": "2021"}
        ],
        "emission_factor": [
            {"value": 0.95, "year": "2023"},
            {"value": 0.97, "year": "2022"},
            {"value": 0.93, "year": "2021"}
        ],
        "fuel_type": [
            {"value": "Coal", "year": "2023"},
            {"value": "Coal", "year": "2022"},
            {"value": "Coal", "year": "2021"}
        ],

        # CRITICAL MANDATORY FIELDS (NEVER NULL - with intelligent defaults)
        "capacity": 300.0,  # Default capacity in MW (will be overridden by extraction)
        "capacity_unit": "MW",
        "technology": "Sub-Critical",  # DEFAULT BOILER TECHNOLOGY (NOT fuel type)
        "boiler_type": "Pulverized Coal",  # Default boiler type (will be overridden by extraction)
        "commencement_date": "2000-01-01T00:00:00.000Z",  # Default date (will be overridden by extraction)

        # Other mandatory fields with intelligent defaults
        "heat_rate": 2200.0,  # Default heat rate for coal plants (will be overridden)
        "heat_rate_unit": "Kcal/kWh",
        "coal_unit_efficiency": 38.0,  # Default efficiency percentage (will be overridden)
        "unit_lifetime": 30,  # Default lifetime in years (will be overridden)
        "remaining_useful_life": 15,  # Default remaining life (will be overridden)

        # Other default fields
        "capex_required_renovation": "default null",
        "emission_factor_coal": "default null",
        "emission_factor_of_gas_unit": "default null",
        "emission_factor_unit": "default null",
        "fgds_status": "default null",
        "ramp_down_rate": "default null",
        "ramp_up_rate": "default null"
    }
    
    # Merge all stage results with debugging
    print(f"🔍 Combining {len(stage_results)} stage results for Unit {unit_number}")
    for i, stage_result in enumerate(stage_results):
        if isinstance(stage_result, dict):
            print(f"🔍 Stage {i+1} contributed {len(stage_result)} fields: {list(stage_result.keys())}")
            combined_data.update(stage_result)
        else:
            print(f"⚠️ Stage {i+1} result is not a dict: {type(stage_result)}")

    # APPLY SOPHISTICATED FALLBACK CALCULATIONS using the engineering engine
    try:
        from agent.fallback_calculations import FallbackCalculationEngine
        fallback_engine = FallbackCalculationEngine()

        print(f"🔧 Applying sophisticated fallback calculations using engineering engine...")

        # CRITICAL: Ensure time series data is NEVER empty (keep existing logic)
        time_series_fields = ["plf", "PAF", "auxiliary_power_consumed", "gross_power_generation", "emission_factor", "fuel_type"]

        for field in time_series_fields:
            if not combined_data.get(field) or combined_data.get(field) == []:
                print(f"🔧 Time series field '{field}' is empty, keeping fallback data")
                # Fallback data is already set in defaults above
            else:
                print(f"✅ Time series field '{field}' has extracted data: {len(combined_data.get(field, []))} entries")

        # CRITICAL: Ensure basic unit fields are NEVER null/empty (keep existing logic)
        critical_fields = {
            "capacity": 300.0,
            "technology": "Sub-Critical",  # DEFAULT BOILER TECHNOLOGY (NOT fuel type)
            "boiler_type": "Pulverized Coal",
            "commencement_date": "2000-01-01T00:00:00.000Z"  # ISO 8601 format with milliseconds
        }

        for field, default_value in critical_fields.items():
            current_value = combined_data.get(field)
            if current_value in [None, "", "default null", "Not available", "null"]:
                combined_data[field] = default_value
                print(f"🔧 Critical field '{field}' was null/empty, set to default: {default_value}")
            else:
                print(f"✅ Critical field '{field}' has valid data: {current_value}")

        # USE SOPHISTICATED FALLBACK ENGINE for missing fields
        enhanced_data = fallback_engine.enhance_unit_data(
            extracted_data=combined_data,
            unit_context=plant_context,
            session_id=f"unit_{unit_number}"
        )

        # Update combined_data with enhanced results
        combined_data.update(enhanced_data)
        print(f"✅ Applied sophisticated fallback calculations using engineering formulas")

    except Exception as e:
        print(f"⚠️ Error applying fallback calculations: {e}")

    # REQUIREMENT 1 & 2: Apply date and efficiency formatting
    try:
        print(f"🔧 Applying date and efficiency formatting for Unit {unit_number}...")

        # Format commencement date
        if "commencement_date" in combined_data:
            original_date = combined_data["commencement_date"]
            formatted_date = format_commencement_date(original_date)
            combined_data["commencement_date"] = formatted_date
            print(f"📅 Formatted commencement_date: '{original_date}' → '{formatted_date}'")

        # Format efficiency fields
        efficiency_fields = [
            "closed_cycle_efficiency",
            "closed_cycle_turbine_efficiency",
            "open_cycle_turbine_efficiency",
            "efficiency_loss_biomass_cofiring"
        ]

        for field in efficiency_fields:
            if field in combined_data:
                original_value = combined_data[field]
                formatted_value = format_efficiency_value(original_value)
                if formatted_value:  # Only update if formatting succeeded
                    combined_data[field] = formatted_value
                    print(f"⚡ Formatted {field}: '{original_value}' → '{formatted_value}'")

        print(f"✅ Date and efficiency formatting complete")

    except Exception as e:
        print(f"⚠️ Error applying date/efficiency formatting: {e}")

    # Debug: Check if critical time series fields were extracted
    time_series_fields = ["annual_operational_hours", "blending_percentage_of_biomass", "emission_factor_coal"]
    for field in time_series_fields:
        if field in combined_data and combined_data[field] != "default null":
            print(f"✅ {field}: {combined_data[field]}")
        else:
            print(f"⚠️ {field}: Missing or default value")

    # AUSTRALIA EXCEL INTEGRATION: Integrate Australia-specific data if applicable
    try:
        # Get country and plant name from plant context
        country = plant_context.get('country', '')
        plant_name = plant_context.get('plant_name', '')

        if country and country.lower() == "australia" and plant_name:
            print(f"🇦🇺 Attempting Australia Excel integration for Unit {unit_number} in {plant_name}")

            # Import and apply Australia Excel integration
            from agent.australia_excel_integration import integrate_australia_excel_data_for_unit

            combined_data = integrate_australia_excel_data_for_unit(
                unit_info=combined_data,
                plant_name=plant_name,
                country=country,
                session_id=f"unit_{unit_number}"
            )

            print(f"✅ Australia Excel integration completed for Unit {unit_number}")
        else:
            if country.lower() != "australia":
                print(f"🌍 Skipping Australia Excel integration - Country: {country}")
            elif not plant_name:
                print(f"⚠️ Skipping Australia Excel integration - No plant name available")

    except Exception as e:
        print(f"⚠️ Error during Australia Excel integration for Unit {unit_number}: {e}")
        import traceback
        traceback.print_exc()

    return combined_data


def format_commencement_date(date_value: str) -> str:
    """
    Format commencement date to yyyy-mm-ddThh:mm:ss.msZ format

    Args:
        date_value: Input date in various formats

    Returns:
        Formatted date string in ISO 8601 format with milliseconds
    """
    import re
    from datetime import datetime

    if not date_value or date_value in ["", "default null", "Not available", "null"]:
        return "2000-01-01T00:00:00.000Z"

    # If already in correct format, return as is
    if re.match(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z', date_value):
        return date_value

    try:
        # Try to parse common date formats first (more specific)
        date_formats = [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%d/%m/%Y",
            "%m/%d/%Y",
            "%Y-%m-%d %H:%M:%S",
            "%Y/%m/%d %H:%M:%S"
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(str(date_value).strip(), fmt)
                return parsed_date.strftime("%Y-%m-%dT%H:%M:%S.000Z")
            except ValueError:
                continue

        # If no format matches, extract year as fallback
        year_match = re.search(r'(\d{4})', str(date_value))
        if year_match:
            year = year_match.group(1)
            # Default to January 1st if only year is available
            return f"{year}-01-01T00:00:00.000Z"

    except Exception as e:
        print(f"⚠️ Error formatting date '{date_value}': {e}")

    # Fallback to default
    return "2000-01-01T00:00:00.000Z"


def format_efficiency_value(value: str) -> str:
    """
    Format efficiency values to decimal format (divide by 100 if percentage)

    Args:
        value: Input efficiency value (could be percentage or decimal)

    Returns:
        Formatted efficiency as decimal string (0.x format)
    """
    if not value or value in ["", "default null", "Not available", "null"]:
        return ""

    try:
        # Remove any non-numeric characters except decimal point and minus
        import re
        clean_value = re.sub(r'[^\d.-]', '', str(value))

        if not clean_value:
            return ""

        numeric_value = float(clean_value)

        # If value is greater than 1, assume it's a percentage and divide by 100
        if numeric_value > 1:
            numeric_value = numeric_value / 100

        # Format to reasonable decimal places
        return f"{numeric_value:.4f}"

    except (ValueError, TypeError) as e:
        print(f"⚠️ Error formatting efficiency value '{value}': {e}")
        return ""


def process_unit_data_formatting(unit_technical_data: dict, session_id: str, plant_uid: str = "") -> dict:
    """
    Format unit data with proper date and efficiency formatting

    Args:
        unit_technical_data: Raw unit data
        session_id: Session ID for logging
        plant_uid: Plant UUID

    Returns:
        Formatted unit data
    """
    print(f"[Session {session_id}] 🔧 Formatting unit data with date and efficiency fixes")

    # Apply date formatting
    if "commencement_date" in unit_technical_data:
        original_date = unit_technical_data["commencement_date"]
        formatted_date = format_commencement_date(original_date)
        unit_technical_data["commencement_date"] = formatted_date
        print(f"[Session {session_id}] 📅 Formatted commencement_date: '{original_date}' → '{formatted_date}'")

    # Apply efficiency formatting (convert to 0.x format)
    efficiency_fields = [
        "closed_cycle_efficiency",
        "closed_cycle_turbine_efficiency",
        "open_cycle_turbine_efficiency",
        "efficiency_loss_biomass_cofiring"
    ]

    for field in efficiency_fields:
        if field in unit_technical_data:
            original_value = unit_technical_data[field]
            formatted_value = format_efficiency_value(original_value)
            if formatted_value:  # Only update if formatting succeeded
                unit_technical_data[field] = formatted_value
                print(f"[Session {session_id}] ⚡ Formatted {field}: '{original_value}' → '{formatted_value}'")

    return unit_technical_data