"""
Australia Excel Data Integration Module

This module handles the integration of Australia-specific power plant data
from the Australia_details.xlsx file. It extracts gross_power_generation 
and emission_factor data for Australian coal power plants.

Key Features:
- Extracts year-wise gross power generation (MWh) data
- Extracts year-wise emission factor (tCO2/MWh) data  
- Only processes coal power plants (as per Excel data availability)
- Integrates with existing pipeline for Australian plants
"""

import os
import pandas as pd
from typing import Dict, List, Optional, Any
import re
from pathlib import Path


class AustraliaExcelIntegrator:
    """
    Handles integration of Australian power plant data from Excel file
    """
    
    def __init__(self, excel_path: Optional[str] = None):
        """
        Initialize the Australia Excel integrator
        
        Args:
            excel_path: Path to Australia_details.xlsx file
        """
        if excel_path is None:
            # Default path relative to backend directory
            # Path: backend/src/agent/australia_excel_integration.py -> backend/Australia_details.xlsx
            excel_path = Path(__file__).parent.parent.parent / "Australia_details.xlsx"
        
        self.excel_path = excel_path
        self.df = None
        self._load_excel_data()
    
    def _load_excel_data(self):
        """Load and preprocess the Excel data"""
        try:
            if not os.path.exists(self.excel_path):
                print(f"❌ Australia Excel file not found: {self.excel_path}")
                return
            
            # Read Excel file
            self.df = pd.read_excel(self.excel_path)
            
            # Clean and preprocess data
            self._preprocess_data()
            
            print(f"✅ Australia Excel data loaded: {len(self.df)} plants")
            
        except Exception as e:
            print(f"❌ Error loading Australia Excel data: {e}")
            self.df = None
    
    def _preprocess_data(self):
        """Preprocess the Excel data for easier access"""
        if self.df is None:
            return

        # The Excel file has descriptive column names like '2020_gross_power_generation', '2020_Emission_factor'
        # We don't need to remove any header row - all rows contain data
        # Parse the column names directly to map years to columns

        # Create a mapping of years to column indices based on column names
        self.year_columns = {}
        self.emission_columns = {}

        # Parse column names to find year and data type patterns
        for i, col_name in enumerate(self.df.columns):
            col_str = str(col_name).lower()

            # Look for year patterns in column names
            for year in range(2020, 2025):
                year_str = str(year)
                if year_str in col_str:
                    if 'gross' in col_str or 'power' in col_str or 'generation' in col_str:
                        # This is a gross power generation column
                        self.year_columns[year] = i
                        print(f"📊 Found {year} gross power generation in column {i}: '{col_name}'")
                    elif 'emission' in col_str or 'factor' in col_str:
                        # This is an emission factor column
                        self.emission_columns[year] = i
                        print(f"📊 Found {year} emission factor in column {i}: '{col_name}'")

        print(f"📊 Final year columns mapped: {self.year_columns}")
        print(f"📊 Final emission columns mapped: {self.emission_columns}")
    
    def is_data_available(self) -> bool:
        """Check if Excel data is available"""
        return self.df is not None and not self.df.empty
    
    def find_plant_data(self, plant_name: str, entity_name: Optional[str] = None) -> Optional[Dict]:
        """
        Find plant data in the Excel file
        
        Args:
            plant_name: Name of the power plant
            entity_name: Optional entity/company name for better matching
            
        Returns:
            Dictionary with plant data or None if not found
        """
        if not self.is_data_available():
            return None
        
        # Clean plant name for matching
        clean_plant_name = self._clean_plant_name(plant_name)
        
        # Try exact match first
        exact_match = self._find_exact_match(clean_plant_name, entity_name)
        if exact_match is not None:
            return exact_match
        
        # Try fuzzy matching
        fuzzy_match = self._find_fuzzy_match(clean_plant_name, entity_name)
        if fuzzy_match is not None:
            return fuzzy_match
        
        print(f"❌ No match found in Australia Excel for: {plant_name}")
        return None
    
    def _clean_plant_name(self, plant_name: str) -> str:
        """Clean plant name for better matching"""
        # Remove common suffixes and normalize
        clean_name = plant_name.lower().strip()
        
        # Remove common power plant suffixes
        suffixes_to_remove = [
            'power station', 'power plant', 'generating station', 
            'generation station', 'thermal power station', 'coal power station'
        ]
        
        for suffix in suffixes_to_remove:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)].strip()
        
        return clean_name
    
    def _find_exact_match(self, clean_plant_name: str, entity_name: Optional[str]) -> Optional[Dict]:
        """Find exact match in Excel data"""
        for idx, row in self.df.iterrows():
            excel_plant_name = str(row['Plant Name']).lower().strip()
            excel_clean_name = self._clean_plant_name(excel_plant_name)
            
            if clean_plant_name == excel_clean_name:
                # If entity name provided, verify it matches
                if entity_name:
                    excel_entity = str(row['Entity Name']).lower().strip()
                    if entity_name.lower().strip() not in excel_entity and excel_entity not in entity_name.lower().strip():
                        continue
                
                return self._extract_plant_data(row)
        
        return None
    
    def _find_fuzzy_match(self, clean_plant_name: str, entity_name: Optional[str]) -> Optional[Dict]:
        """Find fuzzy match in Excel data"""
        best_match = None
        best_score = 0
        
        for idx, row in self.df.iterrows():
            excel_plant_name = str(row['Plant Name']).lower().strip()
            excel_clean_name = self._clean_plant_name(excel_plant_name)
            
            # Simple fuzzy matching - check if one name contains the other
            score = 0
            if clean_plant_name in excel_clean_name or excel_clean_name in clean_plant_name:
                score = 0.8
            elif any(word in excel_clean_name for word in clean_plant_name.split() if len(word) > 3):
                score = 0.6
            
            # Boost score if entity matches
            if entity_name and score > 0:
                excel_entity = str(row['Entity Name']).lower().strip()
                if entity_name.lower().strip() in excel_entity or excel_entity in entity_name.lower().strip():
                    score += 0.2
            
            if score > best_score and score >= 0.6:  # Minimum threshold
                best_score = score
                best_match = row
        
        if best_match is not None:
            print(f"🔍 Fuzzy match found (score: {best_score:.2f}): {best_match['Plant Name']}")
            return self._extract_plant_data(best_match)

        return None

    def _extract_plant_data(self, row) -> Dict:
        """
        Extract plant data from Excel row

        Args:
            row: Pandas row from the Excel data

        Returns:
            Dictionary with structured plant data
        """
        plant_data = {
            "entity_name": str(row['Entity Name']),
            "plant_name": str(row['Plant Name']),
            "data_source": "Australia_details.xlsx",
            "gross_power_generation": {},
            "emission_factor": {},
            "years_available": list(self.year_columns.keys())
        }

        # Extract year-wise data
        for year in self.year_columns.keys():
            try:
                # Get gross power generation
                gross_col_idx = self.year_columns[year]
                gross_value = row.iloc[gross_col_idx]

                # Clean and convert gross power generation
                if pd.notna(gross_value) and gross_value != 0:
                    plant_data["gross_power_generation"][str(year)] = {
                        "value": float(gross_value),
                        "unit": "MWh",
                        "source": "Australia_details.xlsx"
                    }

                # Get emission factor
                if year in self.emission_columns:
                    emission_col_idx = self.emission_columns[year]
                    emission_value = row.iloc[emission_col_idx]

                    # Clean and convert emission factor
                    if pd.notna(emission_value) and emission_value != 0:
                        plant_data["emission_factor"][str(year)] = {
                            "value": float(emission_value),
                            "unit": "tCO2/MWh",
                            "source": "Australia_details.xlsx"
                        }

            except (ValueError, IndexError) as e:
                print(f"⚠️ Error extracting data for year {year}: {e}")
                continue

        return plant_data

    def get_available_plants(self) -> List[Dict]:
        """
        Get list of all available plants in the Excel file

        Returns:
            List of dictionaries with plant information
        """
        if not self.is_data_available():
            return []

        plants = []
        for idx, row in self.df.iterrows():
            plants.append({
                "entity_name": str(row['Entity Name']),
                "plant_name": str(row['Plant Name']),
                "has_data": True
            })

        return plants

    def is_coal_plant_data_available(self, plant_name: str, country: str) -> bool:
        """
        Check if this is an Australian coal plant with data available

        Args:
            plant_name: Name of the power plant
            country: Country of the plant

        Returns:
            True if data is available for this Australian coal plant
        """
        # Only process Australian plants
        if country.lower() != "australia":
            return False

        # Check if plant exists in Excel data
        plant_data = self.find_plant_data(plant_name)
        return plant_data is not None


# Global instance for easy access
_australia_integrator = None

def get_australia_integrator() -> AustraliaExcelIntegrator:
    """Get global Australia Excel integrator instance"""
    global _australia_integrator
    if _australia_integrator is None:
        _australia_integrator = AustraliaExcelIntegrator()
    return _australia_integrator


def integrate_australia_excel_data(plant_info: Dict, country: str) -> Dict:
    """
    Integrate Australia Excel data into plant information

    Args:
        plant_info: Existing plant information dictionary
        country: Country of the plant

    Returns:
        Updated plant information with Excel data integrated
    """
    # Only process Australian plants
    if country.lower() != "australia":
        return plant_info

    integrator = get_australia_integrator()

    # Extract plant name from plant_info
    plant_name = plant_info.get("plant_name", "")
    if not plant_name:
        return plant_info

    # Try to find Excel data
    excel_data = integrator.find_plant_data(
        plant_name=plant_name,
        entity_name=plant_info.get("owner", "")
    )

    if excel_data is None:
        print(f"📊 No Australia Excel data found for: {plant_name}")
        return plant_info

    print(f"✅ Integrating Australia Excel data for: {plant_name}")

    # Integrate gross power generation data
    if "gross_power_generation" in excel_data and excel_data["gross_power_generation"]:
        plant_info["gross_power_generation"] = excel_data["gross_power_generation"]
        print(f"   📈 Added gross power generation data for {len(excel_data['gross_power_generation'])} years")

    # Integrate emission factor data
    if "emission_factor" in excel_data and excel_data["emission_factor"]:
        plant_info["emission_factor"] = excel_data["emission_factor"]
        print(f"   🌿 Added emission factor data for {len(excel_data['emission_factor'])} years")

    # Add metadata about Excel integration
    plant_info["australia_excel_integration"] = {
        "integrated": True,
        "excel_entity_name": excel_data.get("entity_name", ""),
        "excel_plant_name": excel_data.get("plant_name", ""),
        "years_available": excel_data.get("years_available", []),
        "data_source": "Australia_details.xlsx"
    }

    return plant_info


def integrate_australia_excel_data_for_unit(unit_info: Dict, plant_name: str, country: str, session_id: str = "unknown") -> Dict:
    """
    Integrate Australia Excel data into unit-level information

    This function integrates gross_power_generation and emission_factor data
    from the Australia Excel file into unit-level JSON structures.

    Args:
        unit_info: Existing unit information dictionary
        plant_name: Name of the plant this unit belongs to
        country: Country of the plant
        session_id: Session ID for logging

    Returns:
        Updated unit information with Excel data integrated
    """
    # Only process Australian plants
    if country.lower() != "australia":
        return unit_info

    integrator = get_australia_integrator()

    if not plant_name:
        return unit_info

    # Try to find Excel data for the plant
    excel_data = integrator.find_plant_data(
        plant_name=plant_name,
        entity_name=None  # We'll match by plant name only for units
    )

    if excel_data is None:
        print(f"[Session {session_id}] 📊 No Australia Excel data found for unit in plant: {plant_name}")
        return unit_info

    unit_number = unit_info.get("unit_number", "unknown")
    print(f"[Session {session_id}] ✅ Integrating Australia Excel data for Unit {unit_number} in {plant_name}")

    # Convert Excel data to unit-level time-series format
    # Excel format: {"2020": {"value": 15756684.0, "unit": "MWh", "source": "..."}}
    # Unit format: [{"value": "15756684", "year": "2020"}, ...]

    # Integrate gross power generation data
    if "gross_power_generation" in excel_data and excel_data["gross_power_generation"]:
        unit_gross_generation = []
        for year, data in excel_data["gross_power_generation"].items():
            unit_gross_generation.append({
                "value": str(int(data["value"])),  # Convert to string as expected by unit schema
                "year": str(year)
            })

        # Sort by year (newest first)
        unit_gross_generation.sort(key=lambda x: int(x["year"]), reverse=True)

        unit_info["gross_power_generation"] = unit_gross_generation
        print(f"[Session {session_id}]    📈 Added gross power generation data for {len(unit_gross_generation)} years")

        # Log the data for verification
        for entry in unit_gross_generation[:3]:  # Show first 3 entries
            print(f"[Session {session_id}]       {entry['year']}: {entry['value']} MWh")

    # Integrate emission factor data
    if "emission_factor" in excel_data and excel_data["emission_factor"]:
        unit_emission_factor = []
        for year, data in excel_data["emission_factor"].items():
            unit_emission_factor.append({
                "value": str(data["value"]),  # Convert to string as expected by unit schema
                "year": str(year)
            })

        # Sort by year (newest first)
        unit_emission_factor.sort(key=lambda x: int(x["year"]), reverse=True)

        unit_info["emission_factor"] = unit_emission_factor
        print(f"[Session {session_id}]    🌿 Added emission factor data for {len(unit_emission_factor)} years")

        # Log the data for verification
        for entry in unit_emission_factor[:3]:  # Show first 3 entries
            print(f"[Session {session_id}]       {entry['year']}: {entry['value']} tCO2/MWh")

    # Add metadata about Excel integration to unit
    unit_info["australia_excel_integration"] = {
        "integrated": True,
        "excel_entity_name": excel_data.get("entity_name", ""),
        "excel_plant_name": excel_data.get("plant_name", ""),
        "years_available": excel_data.get("years_available", []),
        "data_source": "Australia_details.xlsx",
        "integration_level": "unit"
    }

    return unit_info
