"""
Fallback Calculations Module
Automatically calculates missing power plant data using engineering formulas
when search-based extraction fails to find the information.
"""

import logging
from typing import Dict, List, Optional, Any
from agent.reference_data import CALCULATOR, REFERENCE_DATA
from agent.tools_and_schemas import YearlyData

logger = logging.getLogger(__name__)

class FallbackCalculationEngine:
    """
    Engine that calculates missing power plant data using reference formulas
    """
    
    def __init__(self):
        self.calculator = CALCULATOR
        self.ref_data = REFERENCE_DATA
    
    def enhance_unit_data(self, extracted_data: Dict, unit_context: Dict, session_id: str = "unknown") -> Dict:
        """
        Main method to enhance extracted data with calculated values for missing fields
        
        Args:
            extracted_data: Data extracted from search
            unit_context: Context about the unit (capacity, technology, etc.)
            session_id: Session identifier for logging
            
        Returns:
            Enhanced data with calculated missing fields
        """
        enhanced_data = extracted_data.copy()
        calculations_performed = []
        
        try:
            # Extract basic unit info
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            technology = unit_context.get("technology", "subcritical")
            
            print(f"[Session {session_id}] 🔧 FALLBACK CALCULATIONS: Enhancing unit data")
            print(f"[Session {session_id}] 📊 Unit Context: {capacity}MW {technology}")

            # FIRST: Standardize technology and boiler_type fields for coal plants (do this early)
            self._standardize_technology_field(enhanced_data, session_id)
            self._standardize_boiler_type_field(enhanced_data, session_id)

            # 1. Calculate Plant Load Factor if missing OR if values are unrealistic
            plf_missing = self._is_missing_or_empty(enhanced_data, "plf")
            plf_needs_recalc = False
            
            print(f"[Session {session_id}] 🔍 PLF missing: {plf_missing}")
            if "plf" in enhanced_data:
                print(f"[Session {session_id}] 🔍 Existing PLF data: {enhanced_data['plf']}")
                
                # Check if existing PLF values are unrealistic (>100% or <0%)
                if enhanced_data["plf"]:
                    unrealistic_count = 0
                    total_count = 0
                    for plf_item in enhanced_data["plf"]:
                        if isinstance(plf_item, dict) and "value" in plf_item:
                            plf_val = self._extract_numeric_value(plf_item["value"])
                            total_count += 1
                            if plf_val > 100 or plf_val < 0:
                                unrealistic_count += 1
                                print(f"[Session {session_id}] ⚠️ Unrealistic PLF detected: {plf_val}% for year {plf_item.get('year', 'Unknown')}")
                    
                    # If more than 50% of PLF values are unrealistic, recalculate all
                    if unrealistic_count > 0 and (unrealistic_count / total_count) >= 0.5:
                        plf_needs_recalc = True
                        print(f"[Session {session_id}] 🚨 {unrealistic_count}/{total_count} PLF values are unrealistic - forcing recalculation")
                    elif unrealistic_count > 0:
                        print(f"[Session {session_id}] ⚠️ Some PLF values unrealistic ({unrealistic_count}/{total_count}) - will fix individual values")
            
            if plf_missing or plf_needs_recalc:
                if plf_needs_recalc:
                    print(f"[Session {session_id}] 🔧 Forcing PLF recalculation due to unrealistic values")
                
                plf_data = self._calculate_plf_fallback(enhanced_data, unit_context)
                if plf_data:
                    enhanced_data["plf"] = plf_data
                    calculations_performed.append("PLF")
                    print(f"[Session {session_id}] ✅ Generated PLF: {plf_data}")
            elif "plf" in enhanced_data and enhanced_data["plf"]:
                # Fix individual unrealistic PLF values
                fixed_plf_data = self._fix_unrealistic_plf_values(enhanced_data["plf"], enhanced_data, unit_context, session_id)
                if fixed_plf_data != enhanced_data["plf"]:
                    enhanced_data["plf"] = fixed_plf_data
                    calculations_performed.append("PLF Correction")
                    print(f"[Session {session_id}] ✅ Fixed unrealistic PLF values")
                else:
                    print(f"[Session {session_id}] ✅ PLF values look reasonable, keeping existing data")
            else:
                print(f"[Session {session_id}] ✅ PLF values look reasonable, keeping existing data")
            
            # 2. Calculate Auxiliary Power Consumption if missing
            if self._is_missing_or_empty(enhanced_data, "auxiliary_power_consumed"):
                aux_data = self._calculate_auxiliary_power_fallback(capacity, technology)
                if aux_data:
                    enhanced_data["auxiliary_power_consumed"] = aux_data
                    calculations_performed.append("Auxiliary Power")
            
            # 3. Estimate Plant Efficiency if missing
            if self._is_missing_or_empty(enhanced_data, "coal_unit_efficiency"):
                efficiency = self._calculate_efficiency_fallback(technology)
                if efficiency:
                    enhanced_data["coal_unit_efficiency"] = efficiency
                    calculations_performed.append("Unit Efficiency")

            # 3.5. Calculate heat_rate using formula: heat_rate = 859.85/coal_unit_efficiency
            self._calculate_heat_rate_and_efficiency(enhanced_data, session_id, calculations_performed)

            # 4. Calculate Gross Power Generation if missing OR if values are unrealistic
            gen_missing = self._is_missing_or_empty(enhanced_data, "gross_power_generation")
            gen_needs_recalc = False
            
            print(f"[Session {session_id}] 🔍 Generation missing: {gen_missing}")
            if "gross_power_generation" in enhanced_data:
                existing_gen = enhanced_data["gross_power_generation"]
                if existing_gen:
                    sample_gen = existing_gen[0].get("value", "0") if existing_gen else "0"
                    print(f"[Session {session_id}] 🔍 Existing generation: {sample_gen} (sample)")
                    
                    # Check if generation values are unrealistic (too high for the capacity)
                    gen_val = self._extract_numeric_value(sample_gen)
                    max_theoretical = capacity * 8760  # MW * hours = MWh max possible
                    if gen_val > max_theoretical * 1.1:  # Allow 10% margin for errors
                        gen_needs_recalc = True
                        print(f"[Session {session_id}] ⚠️ Unrealistic generation detected: {gen_val:,.0f} MWh > {max_theoretical:,.0f} MWh max")
            
            if gen_missing or gen_needs_recalc:
                if gen_needs_recalc:
                    print(f"[Session {session_id}] 🔧 Forcing generation recalculation due to unrealistic values")
                
                generation_data = self._calculate_generation_fallback(enhanced_data, unit_context)
                if generation_data:
                    enhanced_data["gross_power_generation"] = generation_data
                    calculations_performed.append("Gross Power Generation")
                    sample_new_gen = generation_data[0].get("value", "0") if generation_data else "0"
                    print(f"[Session {session_id}] ✅ Generated new generation: {sample_new_gen} (sample)")
            else:
                print(f"[Session {session_id}] ✅ Generation values look reasonable, keeping existing data")
            
            # 5. Calculate Emission Factor if missing
            if self._is_missing_or_empty(enhanced_data, "emission_factor"):
                emission_data = self._calculate_emission_factor_fallback(enhanced_data, unit_context)
                if emission_data:
                    enhanced_data["emission_factor"] = emission_data
                    calculations_performed.append("Emission Factor")
            
            # 6. Calculate Plant Availability Factor if missing
            if self._is_missing_or_empty(enhanced_data, "PAF"):
                paf_data = self._calculate_paf_fallback(enhanced_data, unit_context)
                if paf_data:
                    enhanced_data["PAF"] = paf_data
                    calculations_performed.append("PAF")
            
            # 7. Populate Reference Fields (GCV, Technology Parameters)
            reference_fields_populated = self._populate_reference_fields(enhanced_data, unit_context)
            if reference_fields_populated:
                calculations_performed.extend(reference_fields_populated)
            
            # 8. Generate Multi-Year Time Series for all time-series fields
            time_series_enhanced = self._enhance_time_series_data(enhanced_data, unit_context)
            if time_series_enhanced:
                calculations_performed.extend(time_series_enhanced)
            
            # 9. Fix years_percentage format in fuel_type
            fuel_enhanced = self._enhance_fuel_years_percentage(enhanced_data, unit_context)
            if fuel_enhanced:
                calculations_performed.append("Fuel Years Percentage")
            
            # 10. Normalize data types and value ranges to match reference format
            normalization_performed = self._normalize_data_types_and_ranges(enhanced_data, unit_context, session_id)
            if normalization_performed:
                calculations_performed.extend(normalization_performed)
            
            # 11. Clean output format (remove metadata)
            self._clean_output_format(enhanced_data)
            
            # 12. Validate and flag unusual values (but don't add to output)
            validation_results = self.calculator.validate_extracted_values(enhanced_data, unit_context)
            if validation_results:
                # Log validation warnings but don't add to final output
                print(f"[Session {session_id}] ⚠️ Validation warnings: {validation_results}")
                # enhanced_data["_validation_warnings"] = validation_results  # REMOVED
            
            # Log results
            if calculations_performed:
                print(f"[Session {session_id}] ✅ CALCULATIONS COMPLETED: {', '.join(calculations_performed)}")
            else:
                print(f"[Session {session_id}] ℹ️  No fallback calculations needed - all data available")
                
        except Exception as e:
            logger.error(f"[Session {session_id}] Error in fallback calculations: {str(e)}")
            print(f"[Session {session_id}] ❌ FALLBACK CALCULATION ERROR: {str(e)}")
        
        return enhanced_data
    
    def _is_missing_or_empty(self, data: Dict, field: str) -> bool:
        """Check if field is missing or empty"""
        if field not in data:
            return True
        
        value = data[field]
        
        # Handle different field types
        if isinstance(value, list):
            return len(value) == 0
        elif isinstance(value, str):
            return value.strip() in ["", "Not available", "N/A", "Unknown"]
        elif value is None:
            return True
        
        return False
    
    def _extract_numeric_value(self, value: Any) -> float:
        """Extract numeric value from various formats"""
        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            # Remove common non-numeric characters
            cleaned = value.replace(",", "").replace("MW", "").replace("GW", "").replace("%", "").strip()

            # CRITICAL FIX: Check for empty string before float conversion
            if not cleaned or cleaned in ["", "Not available", "N/A", "Unknown", "default null"]:
                return 0.0

            try:
                return float(cleaned)
            except ValueError:
                return 0.0

        return 0.0
    
    def _fix_unrealistic_plf_values(self, plf_data: List[Dict], extracted_data: Dict, unit_context: Dict, session_id: str) -> List[Dict]:
        """Fix individual unrealistic PLF values by recalculating from generation data"""
        try:
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            generation_data = extracted_data.get("gross_power_generation", [])
            
            if capacity <= 0 or not generation_data:
                return plf_data
            
            # Create a lookup of generation data by year
            generation_by_year = {}
            for gen_record in generation_data:
                if isinstance(gen_record, dict) and "value" in gen_record and "year" in gen_record:
                    year = gen_record["year"]
                    generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                    if generation_mwh > 0:
                        generation_by_year[year] = generation_mwh
            
            fixed_plf_data = []
            for plf_item in plf_data:
                if isinstance(plf_item, dict) and "value" in plf_item:
                    plf_val = self._extract_numeric_value(plf_item["value"])
                    year = plf_item.get("year", "Unknown")
                    
                    # If PLF is unrealistic and we have generation data for this year
                    if (plf_val > 100 or plf_val < 0) and year in generation_by_year:
                        generation_mwh = generation_by_year[year]
                        
                        # Calculate correct PLF
                        corrected_plf = self.calculator.calculate_plf(generation_mwh, capacity)
                        
                        # Apply unit detection if still unrealistic
                        if corrected_plf > 100:
                            # The generation data has unit issues - try different conversions
                            
                            # Method 1: Try GWh to MWh conversion (divide by 1000)
                            corrected_generation = generation_mwh / 1000
                            corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                            
                            if 10 <= corrected_plf <= 100:
                                print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}% (GWh→MWh conversion)")
                            else:
                                # Method 2: Try assuming generation is in GWh but read as MWh
                                # This means we need to divide by 1000 again
                                corrected_generation = generation_mwh / 1000000
                                corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                                
                                if 10 <= corrected_plf <= 100:
                                    print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}% (TWh→MWh conversion)")
                                else:
                                    # Method 3: Calculate what generation SHOULD be for reasonable PLF
                                    # Assume 70% PLF as reasonable target
                                    max_generation = capacity * 8760
                                    target_plf = 70.0  # Reasonable PLF
                                    target_generation = (target_plf / 100) * max_generation
                                    
                                    print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {target_plf}% (estimated reasonable PLF)")
                                    corrected_plf = target_plf
                        else:
                            print(f"[Session {session_id}] 🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}%")
                        
                        # Add corrected PLF
                        fixed_plf_data.append({
                            "value": f"{corrected_plf:.1f}%",
                            "year": year,
                            "_corrected": True,
                            "_original_value": plf_item["value"]
                        })
                    else:
                        # Keep original if reasonable or no generation data
                        fixed_plf_data.append(plf_item)
                else:
                    fixed_plf_data.append(plf_item)
            
            return fixed_plf_data
            
        except Exception as e:
            logger.error(f"Error fixing unrealistic PLF values: {str(e)}")
            return plf_data
    
    def _extract_generation_value_with_unit_conversion(self, value_str: str) -> float:
        """Extract generation value and convert to MWh if needed"""
        if not isinstance(value_str, str):
            return self._extract_numeric_value(value_str)
        
        # Check for unit indicators
        value_lower = value_str.lower()
        numeric_value = self._extract_numeric_value(value_str)
        
        if numeric_value <= 0:
            return 0.0
        
        # Convert based on detected units
        if any(unit in value_lower for unit in ['gwh', 'gw-h', 'gw h']):
            # Convert GWh to MWh
            return numeric_value * 1000
        elif any(unit in value_lower for unit in ['twh', 'tw-h', 'tw h']):
            # Convert TWh to MWh
            return numeric_value * 1000000
        elif any(unit in value_lower for unit in ['kwh', 'kw-h', 'kw h']):
            # Convert kWh to MWh
            return numeric_value / 1000
        else:
            # Assume MWh if no unit specified, but validate against capacity
            return numeric_value
    
    def _calculate_plf_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate PLF using the new comprehensive PLF calculator
        Supports all 4 calculation cases with proper unit conversion and validation
        """
        try:
            from agent.plf_generation_calculator import calculate_plf_comprehensive

            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            technology = unit_context.get("technology", "subcritical")
            country = unit_context.get("country", "default")

            if capacity <= 0:
                return None

            # Extract generation data if available
            generation_data = extracted_data.get("gross_power_generation", [])
            net_generation_data = extracted_data.get("net_generation", [])

            plf_data = []
            years_processed = set()

            # First, try to calculate from gross generation data
            if generation_data:
                for gen_record in generation_data:
                    if isinstance(gen_record, dict) and "value" in gen_record:
                        generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                        year = gen_record.get("year", "Unknown")

                        if generation_mwh > 0 and year not in years_processed:
                            try:
                                # Use new PLF calculator - Case 2: Unit-level generation available
                                result = calculate_plf_comprehensive(
                                    gross_unit_generation_mwh=generation_mwh,
                                    unit_capacity_mw=capacity,
                                    technology=technology
                                )

                                # Use yearly PLF data if available, otherwise create single year entry
                                if result.yearly_plf_data:
                                    # Find the matching year in yearly data or use the first entry
                                    yearly_entry = next(
                                        (entry for entry in result.yearly_plf_data if entry['year'] == year),
                                        result.yearly_plf_data[0]  # Fallback to first entry
                                    )
                                    plf_data.append({
                                        "value": yearly_entry["value"],
                                        "year": year,
                                        "_calculated": True,
                                        "_method": f"New PLF Calculator: {result.calculation_method}"
                                    })
                                else:
                                    # Fallback to single PLF value
                                    plf_percentage = result.plf_unit_level * 100
                                    plf_data.append({
                                        "value": f"{plf_percentage:.1f}%",
                                        "year": year,
                                        "_calculated": True,
                                        "_method": f"New PLF Calculator: {result.calculation_method}"
                                    })

                                years_processed.add(year)
                                print(f"🔧 NEW PLF CALC: {year} = {plf_data[-1]['value']} from {generation_mwh:,.0f} MWh")

                            except Exception as e:
                                print(f"⚠️ PLF calculation failed for {year}: {e}")
                                continue

            # Then, try net generation data for remaining years
            if net_generation_data:
                for net_record in net_generation_data:
                    if isinstance(net_record, dict) and "value" in net_record:
                        net_generation_mwh = self._extract_generation_value_with_unit_conversion(net_record["value"])
                        year = net_record.get("year", "Unknown")

                        if net_generation_mwh > 0 and year not in years_processed:
                            try:
                                # Use new PLF calculator - Case 4: Net generation only
                                result = calculate_plf_comprehensive(
                                    net_generation_mwh=net_generation_mwh,
                                    unit_capacity_mw=capacity,
                                    technology=technology
                                )

                                # Use yearly PLF data if available, otherwise create single year entry
                                if result.yearly_plf_data:
                                    # Find the matching year in yearly data or use the first entry
                                    yearly_entry = next(
                                        (entry for entry in result.yearly_plf_data if entry['year'] == year),
                                        result.yearly_plf_data[0]  # Fallback to first entry
                                    )
                                    plf_data.append({
                                        "value": yearly_entry["value"],
                                        "year": year,
                                        "_calculated": True,
                                        "_method": f"New PLF Calculator: {result.calculation_method}"
                                    })
                                else:
                                    # Fallback to single PLF value
                                    plf_percentage = result.plf_unit_level * 100
                                    plf_data.append({
                                        "value": f"{plf_percentage:.1f}%",
                                        "year": year,
                                        "_calculated": True,
                                        "_method": f"New PLF Calculator: {result.calculation_method}"
                                    })

                                years_processed.add(year)
                                print(f"🔧 NEW PLF CALC (Net): {year} = {plf_data[-1]['value']} from {net_generation_mwh:,.0f} MWh net")

                            except Exception as e:
                                print(f"⚠️ Net PLF calculation failed for {year}: {e}")
                                continue

            # If we have some PLF data, fill in missing years and return complete 5-year dataset
            if plf_data:
                # Check if we have all 5 years (2020-2024)
                target_years = ["2024", "2023", "2022", "2021", "2020"]
                existing_years = {record['year'] for record in plf_data}
                missing_years = [year for year in target_years if year not in existing_years]

                if missing_years:
                    # Use the average PLF from existing data to fill missing years
                    existing_plf_values = []
                    for record in plf_data:
                        plf_str = record['value'].replace('%', '')
                        existing_plf_values.append(float(plf_str))

                    avg_plf = sum(existing_plf_values) / len(existing_plf_values)

                    # Add missing years with average PLF and slight variations
                    variations = [0.95, 1.02, 0.98, 1.05, 0.97]
                    for i, year in enumerate(missing_years):
                        variation = variations[i % len(variations)]
                        varied_plf = avg_plf * variation

                        plf_data.append({
                            "value": f"{varied_plf:.1f}%",
                            "year": year,
                            "_calculated": True,
                            "_method": f"New PLF Calculator: Estimated from existing data (avg: {avg_plf:.1f}%)"
                        })

                # Sort by year (newest first)
                plf_data.sort(key=lambda x: int(x['year']), reverse=True)

                print(f"🔧 NEW PLF CALCULATOR: Generated {len(plf_data)} PLF data points (2020-2024)")
                return plf_data

            # Fallback: Use country-based typical PLF (same as before)
            typical_plf = self.calculator.get_country_plf_standard(country, technology)

            # Generate multi-year PLF data with variations
            target_years = ["2024", "2023", "2022", "2021", "2020"]
            variations = [0.95, 1.02, 0.98, 1.05, 0.97]  # Realistic variations

            for i, year in enumerate(target_years):
                variation = variations[i % len(variations)]
                yearly_plf = typical_plf * variation

                plf_data.append({
                    "value": f"{yearly_plf:.1f}%",
                    "year": year,
                    "_calculated": True,
                    "_method": f"Country standard PLF for {country} {technology} plants"
                })

            print(f"🔧 FALLBACK PLF DATA: {typical_plf}% typical for {country} {technology}")
            return plf_data

        except Exception as e:
            logger.error(f"Error in new PLF calculation: {str(e)}")
            # Fallback to old method if new calculator fails
            return self._calculate_plf_fallback_legacy(extracted_data, unit_context)

    def _calculate_plf_fallback_legacy(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Legacy PLF calculation method (fallback if new calculator fails)
        """
        try:
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))

            if capacity <= 0:
                return None

            # First try to calculate from actual generation data
            generation_data = extracted_data.get("gross_power_generation", [])

            if generation_data:
                plf_data = []
                for gen_record in generation_data:
                    if isinstance(gen_record, dict) and "value" in gen_record:
                        generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                        year = gen_record.get("year", "Unknown")

                        if generation_mwh > 0:
                            # Calculate PLF using old method
                            plf = self.calculator.calculate_plf(generation_mwh, capacity)

                            # Apply basic validation
                            if plf > 100:
                                # Try unit conversion
                                corrected_generation = generation_mwh / 1000
                                corrected_plf = self.calculator.calculate_plf(corrected_generation, capacity)
                                if 10 <= corrected_plf <= 100:
                                    plf = corrected_plf
                                else:
                                    plf = 70.0  # Default reasonable PLF

                            if 0 <= plf <= 100:
                                plf_data.append({
                                    "value": f"{plf:.1f}%",
                                    "year": year,
                                    "_calculated": True,
                                    "_method": "Legacy PLF calculation"
                                })

                if plf_data:
                    return plf_data

            # Fallback to country standards
            technology = unit_context.get("technology", "subcritical")
            country = unit_context.get("country", "default")
            typical_plf = self.calculator.get_country_plf_standard(country, technology)

            target_years = ["2024", "2023", "2022", "2021", "2020"]
            plf_data = []
            for year in target_years:
                plf_data.append({
                    "value": f"{typical_plf:.1f}%",
                    "year": year,
                    "_calculated": True,
                    "_method": f"Legacy country standard PLF"
                })

            return plf_data

        except Exception as e:
            logger.error(f"Error in legacy PLF calculation: {str(e)}")
            return None

    def _calculate_auxiliary_power_fallback(self, capacity: float, technology: str) -> Optional[List[Dict]]:
        """Calculate auxiliary power consumption based on capacity and technology"""
        try:
            if capacity <= 0:
                return None
            
            aux_power = self.calculator.estimate_auxiliary_power_consumption(capacity, technology)
            
            return [{
                "value": f"{aux_power}%",
                "year": "Estimated",
                "_calculated": True,
                "_method": f"Industry standard for {capacity}MW {technology} plant"
            }]
            
        except Exception as e:
            logger.error(f"Error calculating auxiliary power fallback: {str(e)}")
            return None
    
    def _calculate_efficiency_fallback(self, technology: str) -> Optional[str]:
        """Estimate plant efficiency based on technology"""
        try:
            efficiency = self.calculator.estimate_plant_efficiency(technology)
            return f"{efficiency}%"
            
        except Exception as e:
            logger.error(f"Error calculating efficiency fallback: {str(e)}")
            return None
    
    def _populate_reference_fields(self, extracted_data: Dict, unit_context: Dict) -> List[str]:
        """Populate reference fields from standard values"""
        populated_fields = []
        
        try:
            # Coal GCV values
            if self._is_missing_or_empty(extracted_data, "gcv_coal"):
                coal_type = "bituminous"  # Default coal type
                if coal_type and coal_type != "Not available":
                    gcv_coal = self.calculator.get_coal_gcv(coal_type)
                    extracted_data["gcv_coal"] = str(gcv_coal)
                    extracted_data["gcv_coal_unit"] = "kCal/kg"
                    populated_fields.append("GCV Coal")
            
            # Biomass GCV values
            if self._is_missing_or_empty(extracted_data, "gcv_biomass"):
                biomass_type = "wood_chips"  # Default biomass type
                if biomass_type and biomass_type != "Not available":
                    gcv_biomass = self.calculator.get_biomass_gcv(biomass_type)
                    extracted_data["gcv_biomass"] = str(gcv_biomass)
                    extracted_data["gcv_biomass_unit"] = "kCal/kg"
                    populated_fields.append("GCV Biomass")
            
            # Natural Gas GCV values
            if self._is_missing_or_empty(extracted_data, "gcv_natural_gas"):
                gcv_gas = self.calculator.get_natural_gas_gcv()
                extracted_data["gcv_natural_gas"] = str(gcv_gas)
                extracted_data["gcv_natural_gas_unit"] = "kcal/scm"
                populated_fields.append("GCV Natural Gas")
            
            # Gas turbine efficiency values
            if self._is_missing_or_empty(extracted_data, "open_cycle_gas_turbine_efficency"):
                ocgt_eff = self.calculator.get_ocgt_efficiency()
                extracted_data["open_cycle_gas_turbine_efficency"] = str(ocgt_eff)
                populated_fields.append("OCGT Efficiency")
            
            if self._is_missing_or_empty(extracted_data, "closed_cylce_gas_turbine_efficency"):
                ccgt_eff = self.calculator.get_ccgt_efficiency()
                extracted_data["closed_cylce_gas_turbine_efficency"] = str(ccgt_eff)
                populated_fields.append("CCGT Efficiency")
            
            # Heat rate values
            if self._is_missing_or_empty(extracted_data, "open_cycle_heat_rate"):
                ocgt_hr = self.calculator.get_ocgt_heat_rate()
                extracted_data["open_cycle_heat_rate"] = str(ocgt_hr)
                populated_fields.append("OCGT Heat Rate")
            
            if self._is_missing_or_empty(extracted_data, "combined_cycle_heat_rate"):
                ccgt_hr = self.calculator.get_ccgt_heat_rate()
                extracted_data["combined_cycle_heat_rate"] = str(ccgt_hr)
                populated_fields.append("CCGT Heat Rate")
            
            # Cofiring efficiency loss
            if self._is_missing_or_empty(extracted_data, "efficiency_loss_biomass_cofiring"):
                cofiring_loss = self.calculator.get_cofiring_efficiency_loss()
                extracted_data["efficiency_loss_biomass_cofiring"] = str(cofiring_loss)
                populated_fields.append("Cofiring Efficiency Loss")
                
        except Exception as e:
            logger.error(f"Error populating reference fields: {str(e)}")
        
        return populated_fields
    
    def _enhance_time_series_data(self, extracted_data: Dict, unit_context: Dict) -> List[str]:
        """Enhance time series fields to have 4-5 years of data (2020-2024)"""
        enhanced_fields = []
        target_years = ["2024", "2023", "2022", "2021", "2020"]
        
        try:
            # Enhance PLF data - but only if we have very limited data
            if "plf" in extracted_data and isinstance(extracted_data["plf"], list):
                # Only expand if we have less than 3 data points
                if len(extracted_data["plf"]) < 3:
                    enhanced_plf = self._expand_time_series(extracted_data["plf"], target_years, "plf")
                    if len(enhanced_plf) > len(extracted_data["plf"]):
                        extracted_data["plf"] = enhanced_plf
                        enhanced_fields.append("PLF Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["plf"])
                    enhanced_fields.append("PLF Format Cleaned")
            
            # Enhance PAF data - but only if we have very limited data
            if "PAF" in extracted_data and isinstance(extracted_data["PAF"], list):
                if len(extracted_data["PAF"]) < 3:
                    enhanced_paf = self._expand_time_series(extracted_data["PAF"], target_years, "paf")
                    if len(enhanced_paf) > len(extracted_data["PAF"]):
                        extracted_data["PAF"] = enhanced_paf
                        enhanced_fields.append("PAF Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["PAF"])
                    enhanced_fields.append("PAF Format Cleaned")
            
            # Enhance auxiliary power data - but only if we have very limited data
            if "auxiliary_power_consumed" in extracted_data and isinstance(extracted_data["auxiliary_power_consumed"], list):
                if len(extracted_data["auxiliary_power_consumed"]) < 3:
                    enhanced_aux = self._expand_time_series(extracted_data["auxiliary_power_consumed"], target_years, "aux_power")
                    if len(enhanced_aux) > len(extracted_data["auxiliary_power_consumed"]):
                        extracted_data["auxiliary_power_consumed"] = enhanced_aux
                        enhanced_fields.append("Auxiliary Power Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["auxiliary_power_consumed"])
                    enhanced_fields.append("Auxiliary Power Format Cleaned")
            
            # Enhance emission factor data - but only if we have very limited data
            if "emission_factor" in extracted_data and isinstance(extracted_data["emission_factor"], list):
                if len(extracted_data["emission_factor"]) < 3:
                    enhanced_emission = self._expand_time_series(extracted_data["emission_factor"], target_years, "emission_factor")
                    if len(enhanced_emission) > len(extracted_data["emission_factor"]):
                        extracted_data["emission_factor"] = enhanced_emission
                        enhanced_fields.append("Emission Factor Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["emission_factor"])
                    enhanced_fields.append("Emission Factor Format Cleaned")
            
            # Enhance generation data - but only if we have very limited data
            if "gross_power_generation" in extracted_data and isinstance(extracted_data["gross_power_generation"], list):
                if len(extracted_data["gross_power_generation"]) < 3:
                    enhanced_generation = self._expand_time_series(extracted_data["gross_power_generation"], target_years, "generation")
                    if len(enhanced_generation) > len(extracted_data["gross_power_generation"]):
                        extracted_data["gross_power_generation"] = enhanced_generation
                        enhanced_fields.append("Generation Multi-Year")
                else:
                    # Clean existing data format but don't add generated data
                    self._clean_time_series_format(extracted_data["gross_power_generation"])
                    enhanced_fields.append("Generation Format Cleaned")
                    
        except Exception as e:
            logger.error(f"Error enhancing time series data: {str(e)}")
        
        return enhanced_fields
    
    def _clean_time_series_format(self, time_series_data: List[Dict]):
        """Clean and standardize time-series data format without adding generated data"""
        for item in time_series_data:
            if isinstance(item, dict):
                # Standardize year format
                if "year" in item:
                    year_str = str(item["year"]).strip()
                    # Extract just the year from complex formats like "2021-22" or "September 2019"
                    import re
                    year_match = re.search(r'\b(20\d{2})\b', year_str)
                    if year_match:
                        item["year"] = year_match.group(1)
                    elif year_str == "N/A":
                        item["year"] = "N/A"
                
                # Clean value format
                if "value" in item:
                    value_str = str(item["value"]).strip()
                    # Remove inconsistent formatting but keep the actual value
                    if value_str.endswith('%'):
                        # Keep percentage format consistent
                        clean_value = value_str.replace('%', '').strip()
                        try:
                            float(clean_value)
                            item["value"] = f"{clean_value}%"
                        except:
                            pass
    
    def _expand_time_series(self, existing_data: List[Dict], target_years: List[str], data_type: str) -> List[Dict]:
        """Expand time series data to cover target years"""
        if not existing_data:
            return existing_data
        
        # Get existing years
        existing_years = {item.get("year") for item in existing_data if item.get("year")}
        enhanced_data = existing_data.copy()
        
        # Calculate average value for missing years
        existing_values = []
        for item in existing_data:
            try:
                value_str = item.get("value", "0")
                # Clean value string (remove units, percentages, etc.)
                clean_value = value_str.replace("%", "").replace("kg CO2e/kWh", "").replace(",", "").strip()
                value = float(clean_value)
                existing_values.append(value)
            except:
                continue
        
        if not existing_values:
            return existing_data
        
        avg_value = sum(existing_values) / len(existing_values)
        
        # Add missing years with slight variations around average
        variations = [0.95, 0.97, 1.02, 1.05, 0.98]  # Small variations to make data realistic
        
        for i, year in enumerate(target_years):
            if year not in existing_years:
                variation = variations[i % len(variations)]
                estimated_value = avg_value * variation
                
                # Format value based on data type
                if data_type in ["plf", "paf", "aux_power"]:
                    formatted_value = f"{estimated_value:.2f}%"
                elif data_type == "emission_factor":
                    formatted_value = f"{estimated_value:.3f}"
                else:
                    formatted_value = f"{estimated_value:.0f}"
                
                enhanced_data.append({
                    "value": formatted_value,
                    "year": year
                })
        
        # Sort by year (newest first)
        enhanced_data.sort(key=lambda x: x.get("year", "0"), reverse=True)
        return enhanced_data

    def _get_fuel_target_years(self, unit_context: Dict, extracted_data: Dict) -> List[str]:
        """Get target years from plant commencement date to 2024"""
        try:
            # Try to get commencement date from various sources
            commencement_date = None

            # Check unit context first
            if "commencement_date" in unit_context:
                commencement_date = unit_context["commencement_date"]
            elif "commencement_date" in extracted_data:
                commencement_date = extracted_data["commencement_date"]

            if commencement_date and commencement_date not in ["", "default null", "Not available", "null"]:
                try:
                    # Parse various date formats
                    import re
                    from datetime import datetime

                    # Extract year from date string (handle various formats)
                    year_match = re.search(r'(\d{4})', str(commencement_date))
                    if year_match:
                        start_year = int(year_match.group(1))

                        # Generate years from commencement to 2024
                        target_years = []
                        for year in range(start_year, 2025):  # 2025 to include 2024
                            target_years.append(str(year))

                        print(f"🔧 Fuel target years: {start_year} to 2024 ({len(target_years)} years)")
                        return target_years

                except Exception as e:
                    print(f"⚠️ Error parsing commencement date '{commencement_date}': {e}")

            # Fallback to default years if no valid commencement date
            print("🔧 Using default fuel target years: 2020-2024")
            return ["2024", "2023", "2022", "2021", "2020"]

        except Exception as e:
            print(f"⚠️ Error getting fuel target years: {e}")
            return ["2024", "2023", "2022", "2021", "2020"]
    
    def _enhance_fuel_years_percentage(self, extracted_data: Dict, unit_context: Dict) -> bool:
        """Fix fuel_type years_percentage format to include multiple years (coal only, exclude biomass)

        New requirements:
        1. Generate years from plant commencement date to 2024
        2. Replace year_percentage values >= 0.95 with 1.0
        """
        try:
            if "fuel_type" not in extracted_data or not extracted_data["fuel_type"]:
                return False

            enhanced = False

            # Get target years from plant commencement date to 2024
            target_years = self._get_fuel_target_years(unit_context, extracted_data)

            # Filter to keep only coal fuel types, exclude biomass
            coal_fuel_types = []

            for fuel_item in extracted_data["fuel_type"]:
                if not isinstance(fuel_item, dict):
                    continue

                fuel_name = fuel_item.get("fuel", "").lower()
                fuel_type = fuel_item.get("type", "").lower()

                # Only keep coal fuel types, exclude biomass
                is_coal = (
                    "coal" in fuel_name or
                    any(coal_type in fuel_type for coal_type in [
                        "bituminous", "sub-bituminous", "lignite", "anthracite"
                    ])
                )

                is_biomass = (
                    "biomass" in fuel_name or
                    "wood" in fuel_type or "pellet" in fuel_type or
                    "pks" in fuel_type or "palm" in fuel_type or
                    "kernel" in fuel_type or "husk" in fuel_type or
                    "shell" in fuel_type
                )

                # Only process coal fuel types
                if not (is_coal and not is_biomass):
                    continue

                coal_fuel_types.append(fuel_item)

                years_percentage = fuel_item.get("years_percentage")

                # If years_percentage is null or empty, generate default data (coal only)
                if not years_percentage:
                    fuel_type = fuel_item.get("fuel", "Coal")

                    # Generate realistic percentages for coal (since biomass is excluded)
                    # Coal percentage should be higher since we're not including biomass cofiring
                    base_percentage = 95  # Coal typically 95% when biomass is excluded
                    variations = [0, -2, 1, -1, 2]  # Small year-to-year variations

                    years_percentage = {}
                    for i, year in enumerate(target_years):
                        variation = variations[i % len(variations)]
                        percentage = base_percentage + variation
                        percentage_decimal = max(0.0, min(1.0, percentage / 100.0))  # Convert to decimal (0-1)

                        # Apply the >= 0.95 rule: replace with 1.0
                        if percentage_decimal >= 0.95:
                            percentage_decimal = 1.0

                        years_percentage[year] = percentage_decimal

                    fuel_item["years_percentage"] = years_percentage
                    enhanced = True

                # Process existing years_percentage data
                else:
                    if isinstance(years_percentage, dict):
                        # First, apply the >= 0.95 rule to existing data and normalize format
                        for year, percentage in list(years_percentage.items()):
                            try:
                                if isinstance(percentage, str):
                                    numeric_value = float(percentage)
                                    if numeric_value > 1:  # If it's a percentage (>1), convert to decimal
                                        numeric_value = numeric_value / 100.0
                                else:
                                    numeric_value = float(percentage)

                                # Apply the >= 0.95 rule
                                if numeric_value >= 0.95:
                                    years_percentage[year] = 1.0
                                else:
                                    years_percentage[year] = max(0.0, min(1.0, numeric_value))

                            except (ValueError, TypeError):
                                # Keep original value if conversion fails
                                continue

                        # Add missing years if needed
                        existing_years = set(years_percentage.keys())
                        missing_years = [year for year in target_years if year not in existing_years]

                        if missing_years:
                            # Calculate average from existing data
                            existing_values = []
                            for year, percentage in years_percentage.items():
                                try:
                                    existing_values.append(float(percentage))
                                except (ValueError, TypeError):
                                    continue

                            if existing_values:
                                avg_percentage = sum(existing_values) / len(existing_values)
                                variations = [0.98, 1.02, 0.99, 1.01, 1.0]  # Small variations

                                for i, year in enumerate(missing_years):
                                    variation = variations[i % len(variations)]
                                    new_percentage = avg_percentage * variation
                                    new_percentage = max(0.0, min(1.0, new_percentage))

                                    # Apply the >= 0.95 rule
                                    if new_percentage >= 0.95:
                                        new_percentage = 1.0

                                    years_percentage[year] = new_percentage

                        enhanced = True

            # Update fuel_type with filtered coal-only entries
            extracted_data["fuel_type"] = coal_fuel_types

            return enhanced

        except Exception as e:
            logger.error(f"Error enhancing fuel years percentage: {str(e)}")
            return False

    def _remove_excluded_fields(self, extracted_data: Dict, session_id: str = "unknown") -> bool:
        """Remove the 19 excluded fields from unit JSON output"""
        excluded_fields = [
            "fuel_source", "capex_required_retrofit_biomass", "open_cycle_gas_turbine_efficency",
            "capex_required_retrofit_biomass_unit", "open_cycle_heat_rate", "gcv_biomass",
            "capex_required_renovation_closed_cycle", "gcv_biomass_unit", "gcv_natural_gas",
            "gcv_natural_gas_unit", "closed_cylce_gas_turbine_efficency", "combined_cycle_heat_rate",
            "capex_required_renovation_closed_cycle_unit", "capex_required_renovation_open_cycle",
            "capex_required_renovation_open_cycle_unit", "emission_factor_gas", "emission_factor_of_gas_unit",
            "capex_required_renovation", "capex_required_renovation_unit", "australia_excel_integration"
        ]

        fields_removed = []
        for field in excluded_fields:
            if field in extracted_data:
                del extracted_data[field]
                fields_removed.append(field)

        if fields_removed:
            print(f"[Session {session_id}] 🚫 Removed excluded fields from fallback: {fields_removed}")
            return True
        else:
            print(f"[Session {session_id}] ℹ️ No excluded fields found to remove in fallback")
            return False
    
    def _clean_output_format(self, extracted_data: Dict):
        """Remove metadata fields from output"""
        try:
            # Clean time series fields
            time_series_fields = ["plf", "PAF", "auxiliary_power_consumed", "emission_factor", "gross_power_generation"]
            
            for field in time_series_fields:
                if field in extracted_data and isinstance(extracted_data[field], list):
                    for item in extracted_data[field]:
                        if isinstance(item, dict):
                            # Remove metadata fields
                            item.pop("_calculated", None)
                            item.pop("_method", None)
                            item.pop("_calculation_details", None)
                            
                            # Clean value format
                            if "value" in item:
                                value = item["value"]
                                if isinstance(value, str):
                                    # Remove unwanted text from values
                                    if "kg CO2e/kWh" in value:
                                        item["value"] = value.replace(" kg CO2e/kWh", "")
                                    elif "%" in value and field == "emission_factor":
                                        # Don't remove % from PLF, PAF, aux_power but remove from emission_factor
                                        item["value"] = value.replace("%", "")
                                    
        except Exception as e:
            logger.error(f"Error cleaning output format: {str(e)}")

    def _calculate_emission_factor_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate emission factor using CSV methods:
        Method 1: (Annual total emission by unit) / (Annual generation by unit) 
        Method 2: Engineering calculation from coal properties
        """
        try:
            # First try Method 1: Look for emission data directly
            emission_method1_data = self._search_for_emission_data(extracted_data)
            generation_data = extracted_data.get("gross_power_generation", [])
            
            if emission_method1_data and generation_data:
                print("🔧 USING METHOD 1: Direct emission data calculation")
                emission_data = []
                
                # Match emission data with generation data by year
                for emission_record in emission_method1_data:
                    emission_value = self._extract_numeric_value(emission_record["value"])
                    year = emission_record.get("year", "Unknown")
                    
                    # Find corresponding generation for same year
                    for gen_record in generation_data:
                        if gen_record.get("year") == year:
                            generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                            if generation_mwh > 0 and emission_value > 0:
                                # Convert generation to kWh
                                generation_kwh = generation_mwh * 1000
                                
                                # Calculate emission factor
                                emission_factor = emission_value / generation_kwh
                                
                                emission_data.append({
                                    "value": f"{emission_factor:.3f}",
                                    "year": year,
                                    "_calculated": True,
                                    "_method": "Method 1: Direct emission/generation calculation"
                                })
                                break
                
                if emission_data:
                    return emission_data
            
            # Method 2: Engineering calculation (existing method)
            fuel_data = extracted_data.get("fuel_type", [])
            
            if not generation_data:
                return None
            
            # Extract coal type from fuel data (coal only, exclude biomass)
            coal_type = "bituminous"  # Default
            for fuel in fuel_data:
                if isinstance(fuel, dict):
                    fuel_name = fuel.get("fuel", "").lower()
                    fuel_type = fuel.get("type", "").lower()

                    # Only process coal fuel types, skip biomass
                    is_coal = "coal" in fuel_name or any(ct in fuel_type for ct in ["bituminous", "sub-bituminous", "lignite", "anthracite"])
                    is_biomass = "biomass" in fuel_name or any(bt in fuel_type for bt in ["wood", "pellet", "pks", "palm", "kernel", "husk", "shell"])

                    if is_coal and not is_biomass:
                        if "sub" in fuel_type and "bituminous" in fuel_type:
                            coal_type = "sub_bituminous"
                        elif "bituminous" in fuel_type:
                            coal_type = "bituminous"
                        elif "lignite" in fuel_type:
                            coal_type = "lignite"
                        elif "anthracite" in fuel_type:
                            coal_type = "anthracite"
            
            # Get technology and capacity
            technology = unit_context.get("technology", "subcritical")
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            
            emission_data = []
            for gen_record in generation_data:
                if isinstance(gen_record, dict) and "value" in gen_record:
                    generation_mwh = self._extract_generation_value_with_unit_conversion(gen_record["value"])
                    year = gen_record.get("year", "Unknown")
                    
                    if generation_mwh > 0:
                        # Convert MWh to kWh
                        generation_kwh = generation_mwh * 1000
                        
                        # Calculate emission factor
                        result = self.calculator.calculate_emission_factor_from_coal(
                            generation_kwh, coal_type, technology=technology
                        )
                        
                        emission_data.append({
                            "value": f"{result['emission_factor']} kg CO2e/kWh",
                            "year": year,
                            "_calculated": True,
                            "_method": f"Calculated from {coal_type} coal and {technology} technology",
                            "_calculation_details": result["calculation_details"]
                        })
            
            return emission_data if emission_data else None
            
        except Exception as e:
            logger.error(f"Error calculating emission factor fallback: {str(e)}")
            return None
    
    def _calculate_paf_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate PAF following CSV requirements:
        1. First try to find 'No.of hours plant was available' 
        2. If not found, set PAF = 1 for each year
        """
        try:
            # First check if we have any availability hours data from search
            availability_data = self._search_for_availability_hours(extracted_data)
            
            if availability_data:
                # Calculate PAF from availability hours
                paf_data = []
                for avail_record in availability_data:
                    available_hours = self._extract_numeric_value(avail_record["value"])
                    year = avail_record.get("year", "Unknown")
                    total_hours = 8760  # Total hours in a year
                    
                    paf_value = (available_hours / total_hours) * 100
                    paf_data.append({
                        "value": f"{paf_value:.1f}%",
                        "year": year,
                        "_calculated": True,
                        "_method": "Calculated from availability hours data"
                    })
                
                return paf_data
            
            # If no availability hours found, set PAF = 1 for each year as per requirement
            target_years = ["2024", "2023", "2022", "2021", "2020"]
            paf_data = []
            
            for year in target_years:
                paf_data.append({
                    "value": "1",  # Set PAF = 1 as per requirement
                    "year": year,
                    "_calculated": True,
                    "_method": "Default fallback (availability hours not found)"
                })
            
            return paf_data
            
        except Exception as e:
            logger.error(f"Error calculating PAF fallback: {str(e)}")
            return None
    
    def _calculate_generation_fallback(self, extracted_data: Dict, unit_context: Dict) -> Optional[List[Dict]]:
        """
        Calculate gross power generation using country-based industry standards
        Following CSV requirement: Use industry standard values based on plant country
        """
        try:
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            technology = unit_context.get("technology", "subcritical")
            country = unit_context.get("country", "default")
            
            if capacity <= 0:
                return None
            
            print(f"🔧 GENERATING POWER GENERATION DATA: {capacity}MW {technology} plant in {country}")
            
            # Calculate generation using country standards
            annual_generation_mwh = self.calculator.calculate_generation_from_country_standards(
                capacity, country, technology
            )
            
            # Generate multi-year data with slight variations
            target_years = ["2024", "2023", "2022", "2021", "2020"]
            variations = [0.98, 1.02, 0.97, 1.05, 0.99]  # Realistic year-to-year variations
            
            generation_data = []
            for i, year in enumerate(target_years):
                variation = variations[i % len(variations)]
                yearly_generation = annual_generation_mwh * variation
                
                generation_data.append({
                    "value": f"{yearly_generation:.0f}",
                    "year": year,
                    "_calculated": True,
                    "_method": f"Country standard for {country} {technology} plants"
                })
            
            print(f"✅ GENERATED POWER DATA: {len(generation_data)} years, avg {annual_generation_mwh:.0f} MWh/year")
            return generation_data
            
        except Exception as e:
            logger.error(f"Error calculating generation fallback: {str(e)}")
            return None
    
    def _search_for_availability_hours(self, extracted_data: Dict) -> Optional[List[Dict]]:
        """Search for availability hours data in extracted data"""
        # Look for availability-related data that might have been extracted
        availability_keywords = [
            "availability_hours", "available_hours", "operational_hours", 
            "plant_availability", "hours_available", "availability_factor"
        ]
        
        for keyword in availability_keywords:
            if keyword in extracted_data:
                data = extracted_data[keyword]
                if isinstance(data, list) and data:
                    return data
        
        return None
    
    def _search_for_emission_data(self, extracted_data: Dict) -> Optional[List[Dict]]:
        """Search for direct emission data in extracted data"""
        # Look for emission-related data that might have been extracted
        emission_keywords = [
            "annual_emissions", "total_emissions", "co2_emissions", 
            "carbon_emissions", "emission_data", "emissions", "co2_data"
        ]
        
        for keyword in emission_keywords:
            if keyword in extracted_data:
                data = extracted_data[keyword]
                if isinstance(data, list) and data:
                    return data
        
        return None
    
    def _normalize_data_types_and_ranges(self, enhanced_data: Dict, unit_context: Dict, session_id: str) -> List[str]:
        """
        Normalize data types and value ranges to match reference.json format
        
        Args:
            enhanced_data: Data to normalize
            unit_context: Unit context information
            session_id: Session identifier for logging
            
        Returns:
            List of normalization operations performed
        """
        normalizations_performed = []
        
        try:
            print(f"[Session {session_id}] 🔧 NORMALIZING DATA TYPES: Starting data type and range normalization")
            
            # 1. Normalize PLF values (percentage strings to decimal fractions)
            if "plf" in enhanced_data and enhanced_data["plf"]:
                plf_normalized = self._normalize_percentage_time_series(enhanced_data["plf"], "PLF", session_id)
                if plf_normalized:
                    enhanced_data["plf"] = plf_normalized
                    normalizations_performed.append("PLF Data Types")
            
            # 2. Normalize Auxiliary Power Consumed (percentage strings to decimal fractions)
            if "auxiliary_power_consumed" in enhanced_data and enhanced_data["auxiliary_power_consumed"]:
                aux_normalized = self._normalize_percentage_time_series(enhanced_data["auxiliary_power_consumed"], "Auxiliary Power", session_id)
                if aux_normalized:
                    enhanced_data["auxiliary_power_consumed"] = aux_normalized
                    normalizations_performed.append("Auxiliary Power Data Types")
            
            # 3. Normalize PAF values (string to numeric)
            if "PAF" in enhanced_data and enhanced_data["PAF"]:
                paf_normalized = self._normalize_numeric_time_series(enhanced_data["PAF"], "PAF", session_id)
                if paf_normalized:
                    enhanced_data["PAF"] = paf_normalized
                    normalizations_performed.append("PAF Data Types")
            
            # 4. Normalize Gross Power Generation (string to numeric, handle unit conversion)
            if "gross_power_generation" in enhanced_data and enhanced_data["gross_power_generation"]:
                gen_normalized = self._normalize_generation_data(enhanced_data["gross_power_generation"], unit_context, session_id)
                if gen_normalized:
                    enhanced_data["gross_power_generation"] = gen_normalized
                    normalizations_performed.append("Generation Data Types")
            
            # 5. Normalize Emission Factor (string to numeric)
            if "emission_factor" in enhanced_data and enhanced_data["emission_factor"]:
                emission_normalized = self._normalize_numeric_time_series(enhanced_data["emission_factor"], "Emission Factor", session_id)
                if emission_normalized:
                    enhanced_data["emission_factor"] = emission_normalized
                    normalizations_performed.append("Emission Factor Data Types")
            
            # 6. Normalize Coal Unit Efficiency (percentage string to decimal)
            if "coal_unit_efficiency" in enhanced_data and enhanced_data["coal_unit_efficiency"]:
                efficiency_normalized = self._normalize_percentage_value(enhanced_data["coal_unit_efficiency"], "Coal Unit Efficiency", session_id)
                if efficiency_normalized is not None:
                    enhanced_data["coal_unit_efficiency"] = efficiency_normalized
                    normalizations_performed.append("Efficiency Data Types")
            
            # 7. Normalize GCV values (string to numeric)
            gcv_fields = ["gcv_coal", "gcv_biomass", "gcv_natural_gas"]
            for field in gcv_fields:
                if field in enhanced_data and enhanced_data[field]:
                    gcv_normalized = self._normalize_numeric_value(enhanced_data[field], field, session_id)
                    if gcv_normalized is not None:
                        enhanced_data[field] = gcv_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            # 8. Normalize Fuel Years Percentage (align with reference format)
            if "fuel_type" in enhanced_data and enhanced_data["fuel_type"]:
                fuel_normalized = self._normalize_fuel_years_percentage(enhanced_data["fuel_type"], session_id)
                if fuel_normalized:
                    enhanced_data["fuel_type"] = fuel_normalized
                    normalizations_performed.append("Fuel Years Percentage Format")
            
            # 9. Normalize efficiency values to 0.x format (divide by 100 if percentage)
            efficiency_fields = [
                "open_cycle_gas_turbine_efficency",
                "closed_cylce_gas_turbine_efficency",
                "efficiency_loss_biomass_cofiring",
                "closed_cycle_efficiency",
                "closed_cycle_turbine_efficiency",
                "open_cycle_turbine_efficiency",
                "coal_unit_efficiency"  # Updated field name
            ]
            for field in efficiency_fields:
                if field in enhanced_data and enhanced_data[field]:
                    eff_normalized = self._normalize_efficiency_value(enhanced_data[field], field, session_id)
                    if eff_normalized is not None:
                        enhanced_data[field] = eff_normalized
                        normalizations_performed.append(f"{field} Efficiency Format")
            
            # 10. Normalize heat rate values
            heat_rate_fields = ["combined_cycle_heat_rate", "open_cycle_heat_rate"]
            for field in heat_rate_fields:
                if field in enhanced_data and enhanced_data[field]:
                    hr_normalized = self._normalize_numeric_value(enhanced_data[field], field, session_id)
                    if hr_normalized is not None:
                        enhanced_data[field] = hr_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            # 11. Remove excluded fields from unit JSON output
            excluded_fields_removed = self._remove_excluded_fields(enhanced_data, session_id)
            if excluded_fields_removed:
                normalizations_performed.append("Excluded fields removal")

            # 12. Normalize standalone numeric fields
            standalone_numeric_fields = [
                "capacity", "unit_lifetime", "remaining_useful_life",
                "annual_operational_hours", "blending_percentage_of_biomass"
            ]
            for field in standalone_numeric_fields:
                if field in enhanced_data and enhanced_data[field] and enhanced_data[field] != "default null":
                    numeric_normalized = self._normalize_numeric_value(enhanced_data[field], field, session_id)
                    if numeric_normalized is not None:
                        enhanced_data[field] = numeric_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            if normalizations_performed:
                print(f"[Session {session_id}] ✅ DATA TYPE NORMALIZATION COMPLETED: {', '.join(normalizations_performed)}")
            else:
                print(f"[Session {session_id}] ℹ️  No data type normalization needed")
            
            # 12. Calculate remaining useful life if missing
            remaining_life_calculated = self._calculate_remaining_useful_life(enhanced_data, session_id)
            if remaining_life_calculated:
                normalizations_performed.append("Remaining Useful Life Calculation")

            # 13. Set fgds_status to null always
            enhanced_data["fgds_status"] = None
            normalizations_performed.append("FGDS Status Set to Null")


                
        except Exception as e:
            logger.error(f"[Session {session_id}] Error in data type normalization: {str(e)}")
            print(f"[Session {session_id}] ❌ DATA TYPE NORMALIZATION ERROR: {str(e)}")
        
        return normalizations_performed
    
    def _normalize_percentage_time_series(self, data: List[Dict], field_name: str, session_id: str) -> Optional[List[Dict]]:
        """Convert percentage string time series to decimal fractions"""
        try:
            normalized_data = []
            changes_made = False
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    original_year = item.get("year")
                    
                    normalized_item = item.copy()
                    
                    # Convert percentage string to decimal
                    if isinstance(original_value, str) and "%" in original_value:
                        # Remove % and convert to decimal
                        numeric_str = original_value.replace("%", "").strip()
                        try:
                            percentage_value = float(numeric_str)
                            decimal_value = percentage_value / 100.0  # Convert to decimal fraction
                            normalized_item["value"] = decimal_value
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} value: {original_value} → {decimal_value}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    # Convert string year to integer
                    if isinstance(original_year, str):
                        try:
                            year_int = int(original_year)
                            normalized_item["year"] = year_int
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} year: '{original_year}' → {year_int}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    normalized_data.append(normalized_item)
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} percentage time series: {str(e)}")
            return None
    
    def _normalize_numeric_time_series(self, data: List[Dict], field_name: str, session_id: str) -> Optional[List[Dict]]:
        """Convert string time series to numeric values"""
        try:
            normalized_data = []
            changes_made = False
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    original_year = item.get("year")
                    
                    normalized_item = item.copy()
                    
                    # Convert string value to numeric
                    if isinstance(original_value, str):
                        try:
                            # Remove commas and whitespace
                            clean_value = original_value.replace(",", "").strip()
                            numeric_value = float(clean_value)
                            normalized_item["value"] = numeric_value
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} value: '{original_value}' → {numeric_value}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    # Convert string year to integer
                    if isinstance(original_year, str):
                        try:
                            year_int = int(original_year)
                            normalized_item["year"] = year_int
                            changes_made = True
                            print(f"[Session {session_id}] 🔧 {field_name} year: '{original_year}' → {year_int}")
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    normalized_data.append(normalized_item)
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} numeric time series: {str(e)}")
            return None
    
    def _normalize_generation_data(self, data: List[Dict], unit_context: Dict, session_id: str) -> Optional[List[Dict]]:
        """Convert generation data with unit conversion (kWh to MWh if needed)"""
        try:
            normalized_data = []
            changes_made = False
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    
                    if isinstance(original_value, str):
                        try:
                            numeric_value = float(original_value)
                            
                            # Check if value seems to be in kWh (too large for MWh)
                            # Compare with reference data ranges - if value > 10,000 MWh, likely in kWh
                            # Reference generation values are typically 2,000-6,000 MWh range
                            if numeric_value > 10000:
                                # Convert from kWh to MWh
                                converted_value = numeric_value / 1000
                                print(f"[Session {session_id}] 🔧 Generation: {original_value} kWh → {converted_value:.2f} MWh")
                                final_value = converted_value
                            else:
                                final_value = numeric_value
                            
                            normalized_item = item.copy()
                            normalized_item["value"] = final_value
                            normalized_data.append(normalized_item)
                            changes_made = True
                            
                        except ValueError:
                            normalized_data.append(item)  # Keep original if conversion fails
                    else:
                        normalized_data.append(item)  # Keep non-string values as-is
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            logger.error(f"Error normalizing generation data: {str(e)}")
            return None
    
    def _normalize_percentage_value(self, value: Any, field_name: str, session_id: str) -> Optional[float]:
        """Convert percentage string to decimal fraction"""
        try:
            if isinstance(value, str) and "%" in value:
                # Remove % and convert to decimal
                numeric_str = value.replace("%", "").strip()
                try:
                    percentage_value = float(numeric_str)
                    decimal_value = percentage_value / 100.0  # Convert to decimal fraction
                    
                    print(f"[Session {session_id}] 🔧 {field_name}: {value} → {decimal_value}")
                    return decimal_value
                except ValueError:
                    return None
            
            return None  # No change needed
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} percentage value: {str(e)}")
            return None
    
    def _normalize_numeric_value(self, value: Any, field_name: str, session_id: str) -> Optional[float]:
        """Convert string to numeric value, handling commas and various formats"""
        try:
            if isinstance(value, str):
                try:
                    # Remove commas and whitespace
                    clean_value = value.replace(",", "").strip()
                    numeric_value = float(clean_value)
                    print(f"[Session {session_id}] 🔧 {field_name}: '{value}' → {numeric_value}")
                    return numeric_value
                except ValueError:
                    return None
            
            return None  # No change needed
            
        except Exception as e:
            logger.error(f"Error normalizing {field_name} numeric value: {str(e)}")
            return None
    
    def _normalize_fuel_years_percentage(self, fuel_data: List[Dict], session_id: str) -> Optional[List[Dict]]:
        """Normalize fuel years percentage to match reference format and apply >= 0.95 rule"""
        try:
            normalized_data = []
            changes_made = False

            for fuel_item in fuel_data:
                if isinstance(fuel_item, dict) and "years_percentage" in fuel_item:
                    years_percentage = fuel_item["years_percentage"]

                    if isinstance(years_percentage, dict):
                        normalized_years = {}

                        for year, percentage in years_percentage.items():
                            try:
                                if isinstance(percentage, str):
                                    # Convert string percentage to decimal fraction
                                    numeric_percentage = float(percentage)
                                    if numeric_percentage > 1:  # If it's a percentage (>1), convert to decimal
                                        decimal_fraction = numeric_percentage / 100.0
                                    else:
                                        decimal_fraction = numeric_percentage
                                else:
                                    decimal_fraction = float(percentage)

                                # Apply the >= 0.95 rule: replace with 1.0
                                if decimal_fraction >= 0.95:
                                    decimal_fraction = 1.0
                                    changes_made = True

                                normalized_years[year] = max(0.0, min(1.0, decimal_fraction))
                                changes_made = True

                            except (ValueError, TypeError):
                                normalized_years[year] = percentage  # Keep original if conversion fails

                        normalized_fuel = fuel_item.copy()
                        normalized_fuel["years_percentage"] = normalized_years
                        normalized_data.append(normalized_fuel)
                    else:
                        normalized_data.append(fuel_item)
                else:
                    normalized_data.append(fuel_item)

            if changes_made:
                print(f"[Session {session_id}] 🔧 Fuel Years Percentage: Applied >= 0.95 rule and normalized to decimal format")
                return normalized_data

            return None  # No changes needed

        except Exception as e:
            logger.error(f"Error normalizing fuel years percentage: {str(e)}")
            return None
    
    def _calculate_remaining_useful_life(self, enhanced_data: Dict, session_id: str) -> bool:
        """
        Calculate remaining useful life if missing, using available data
        
        Returns:
            bool: True if calculation was performed, False otherwise
        """
        try:
            from datetime import datetime
            import re
            
            current_year = datetime.now().year
            remaining_life = enhanced_data.get("remaining_useful_life", "")
            
            # Skip if already has a valid numeric value
            if isinstance(remaining_life, (int, float)) and remaining_life > 0:
                return False
            
            # Skip if already has a valid date
            if isinstance(remaining_life, str) and remaining_life and remaining_life not in ["", "Not available", "default null"]:
                # Check if it's already a valid date format
                if re.match(r'\d{4}-\d{2}-\d{2}', remaining_life):
                    return False
            
            print(f"[Session {session_id}] 🔍 Attempting to calculate remaining_useful_life...")
            
            # Method 1: Calculate from commencement_date + unit_lifetime
            commencement_date = enhanced_data.get("commencement_date", "")
            unit_lifetime = enhanced_data.get("unit_lifetime", "")
            
            if commencement_date and unit_lifetime:
                try:
                    # Extract year from commencement_date
                    commencement_year = None
                    if isinstance(commencement_date, str):
                        year_match = re.search(r'(\d{4})', commencement_date)
                        if year_match:
                            commencement_year = int(year_match.group(1))
                    
                    # Convert unit_lifetime to numeric
                    lifetime_years = None
                    if isinstance(unit_lifetime, (int, float)):
                        lifetime_years = float(unit_lifetime)
                    elif isinstance(unit_lifetime, str):
                        try:
                            lifetime_years = float(unit_lifetime)
                        except ValueError:
                            pass
                    
                    if commencement_year and lifetime_years:
                        end_of_life_year = int(commencement_year + lifetime_years)
                        # Format as date: yyyy-mm-ddThh:mm:ss.msZ
                        end_of_life_date = f"{end_of_life_year}-01-01T00:00:00.000Z"

                        enhanced_data["remaining_useful_life"] = end_of_life_date
                        print(f"[Session {session_id}] ✅ Calculated remaining_useful_life: {end_of_life_date}")
                        print(f"[Session {session_id}]    Based on: commencement({commencement_year}) + lifetime({lifetime_years})")
                        return True
                        
                except Exception as calc_error:
                    print(f"[Session {session_id}] ⚠️ Error in Method 1 calculation: {calc_error}")
            
            # Method 2: Use typical unit lifetime based on technology
            technology = enhanced_data.get("technology", "").lower()
            capacity = enhanced_data.get("capacity", "")
            
            if technology and commencement_date:
                try:
                    # Extract commencement year
                    commencement_year = None
                    if isinstance(commencement_date, str):
                        year_match = re.search(r'(\d{4})', commencement_date)
                        if year_match:
                            commencement_year = int(year_match.group(1))
                    
                    if commencement_year:
                        # Typical lifetimes by technology
                        typical_lifetimes = {
                            "coal": 40,
                            "natural gas": 30,
                            "gas": 30,
                            "biomass": 25,
                            "nuclear": 60,
                            "hydro": 80,
                            "wind": 25,
                            "solar": 25
                        }
                        
                        # Find matching technology
                        estimated_lifetime = None
                        for tech_key, lifetime in typical_lifetimes.items():
                            if tech_key in technology:
                                estimated_lifetime = lifetime
                                break
                        
                        if estimated_lifetime:
                            end_of_life_year = int(commencement_year + estimated_lifetime)
                            # Format as date: yyyy-mm-ddThh:mm:ss.msZ
                            end_of_life_date = f"{end_of_life_year}-01-01T00:00:00.000Z"

                            enhanced_data["remaining_useful_life"] = end_of_life_date
                            print(f"[Session {session_id}] ✅ Estimated remaining_useful_life: {end_of_life_date}")
                            print(f"[Session {session_id}]    Based on: commencement({commencement_year}) + typical_lifetime({estimated_lifetime})")
                            return True
                            
                except Exception as calc_error:
                    print(f"[Session {session_id}] ⚠️ Error in Method 2 calculation: {calc_error}")
            
            # Method 3: Default estimation based on current year and typical retirement patterns
            if not enhanced_data.get("remaining_useful_life") or enhanced_data.get("remaining_useful_life") in ["", "Not available", "default null"]:
                # For coal plants built before 2000, assume retirement by 2035-2040
                # For newer plants, assume 30-40 year lifetime
                if commencement_date:
                    try:
                        year_match = re.search(r'(\d{4})', str(commencement_date))
                        if year_match:
                            commencement_year = int(year_match.group(1))
                            
                            if commencement_year < 2000:
                                # Older plants - assume retirement by 2040
                                end_of_life_year = 2040
                            else:
                                # Newer plants - assume 35 year lifetime
                                end_of_life_year = commencement_year + 35

                            # Format as date: yyyy-mm-ddThh:mm:ss.msZ
                            end_of_life_date = f"{int(end_of_life_year)}-01-01T00:00:00.000Z"
                            enhanced_data["remaining_useful_life"] = end_of_life_date
                            print(f"[Session {session_id}] ✅ Default estimated remaining_useful_life: {end_of_life_date}")
                            print(f"[Session {session_id}]    Based on: default estimation for commencement year {commencement_year}")
                            return True
                            
                    except Exception as calc_error:
                        print(f"[Session {session_id}] ⚠️ Error in Method 3 calculation: {calc_error}")
            
            print(f"[Session {session_id}] ℹ️ Could not calculate remaining_useful_life - insufficient data")
            return False

        except Exception as e:
            logger.error(f"[Session {session_id}] Error calculating remaining useful life: {str(e)}")
            print(f"[Session {session_id}] ❌ REMAINING USEFUL LIFE CALCULATION ERROR: {str(e)}")
            return False

    def _standardize_technology_field(self, enhanced_data: Dict, session_id: str) -> bool:
        """
        Standardize technology field for coal power plants to use only these 4 terms:
        - Subcritical
        - Supercritical
        - Ultra-Supercritical
        - Advanced Ultra-Supercritical

        Returns:
            bool: True if standardization was performed, False otherwise
        """
        try:
            technology = enhanced_data.get("technology", "")
            if not technology or not isinstance(technology, str):
                return False

            # Convert to lowercase for matching
            tech_lower = technology.lower().strip()

            # Define standardization mapping
            technology_mapping = {
                # Subcritical variations
                "sub-critical": "Subcritical",
                "subcritical": "Subcritical",
                "sub critical": "Subcritical",
                "critical": "Subcritical",  # Sometimes just "critical" refers to subcritical

                # Supercritical variations
                "super-critical": "Supercritical",
                "supercritical": "Supercritical",
                "super critical": "Supercritical",

                # Ultra-Supercritical variations
                "ultra-super-critical": "Ultra-Supercritical",
                "ultra-supercritical": "Ultra-Supercritical",
                "ultra super critical": "Ultra-Supercritical",
                "ultra supercritical": "Ultra-Supercritical",
                "usc": "Ultra-Supercritical",

                # Advanced Ultra-Supercritical variations
                "advanced ultra-super-critical": "Advanced Ultra-Supercritical",
                "advanced ultra-supercritical": "Advanced Ultra-Supercritical",
                "advanced ultra super critical": "Advanced Ultra-Supercritical",
                "advanced ultra supercritical": "Advanced Ultra-Supercritical",
                "ausc": "Advanced Ultra-Supercritical",
                "a-usc": "Advanced Ultra-Supercritical"
            }

            # Check if technology contains coal-related terms OR critical/supercritical patterns
            # (standardize if it has coal terms OR if it matches critical patterns)
            is_coal_plant = any(coal_term in tech_lower for coal_term in [
                "coal", "lignite", "bituminous", "anthracite", "sub-bituminous"
            ])

            # Also standardize if it matches critical/supercritical patterns (likely coal plants)
            has_critical_pattern = any(pattern in tech_lower for pattern in [
                "critical", "supercritical", "sub-critical", "super-critical",
                "ultra-supercritical", "ultra-super-critical", "usc", "ausc"
            ])

            if not is_coal_plant and not has_critical_pattern:
                # For non-coal plants without critical patterns, don't standardize
                print(f"[Session {session_id}] ℹ️ Technology not standardized (non-coal or unrecognized): '{technology}'")
                return False

            # Find matching standardized term (prioritize longer patterns first)
            standardized_tech = None
            # Sort by length descending to match longer patterns first
            sorted_mappings = sorted(technology_mapping.items(), key=lambda x: len(x[0]), reverse=True)
            for variant, standard in sorted_mappings:
                if variant in tech_lower:
                    standardized_tech = standard
                    break

            # If no exact match found, try partial matching for common patterns
            if not standardized_tech:
                if "ultra" in tech_lower and ("super" in tech_lower or "critical" in tech_lower):
                    if "advanced" in tech_lower:
                        standardized_tech = "Advanced Ultra-Supercritical"
                    else:
                        standardized_tech = "Ultra-Supercritical"
                elif "super" in tech_lower and "critical" in tech_lower:
                    standardized_tech = "Supercritical"
                elif "critical" in tech_lower or "sub" in tech_lower:
                    standardized_tech = "Subcritical"

            # Apply standardization if found
            if standardized_tech and standardized_tech != technology:
                enhanced_data["technology"] = standardized_tech
                print(f"[Session {session_id}] 🔧 Technology standardized: '{technology}' → '{standardized_tech}'")
                return True
            elif standardized_tech == technology:
                print(f"[Session {session_id}] ✅ Technology already standardized: '{technology}'")
                return False
            else:
                print(f"[Session {session_id}] ℹ️ Technology not standardized (non-coal or unrecognized): '{technology}'")
                return False

        except Exception as e:
            logger.error(f"[Session {session_id}] Error standardizing technology field: {str(e)}")
            print(f"[Session {session_id}] ❌ TECHNOLOGY STANDARDIZATION ERROR: {str(e)}")
            return False

    def _standardize_boiler_type_field(self, enhanced_data: Dict, session_id: str) -> bool:
        """
        Standardize boiler_type field for coal power plants to use only these 4 terms:
        - Mechanical Stoker-Fired
        - Pulverized Coal
        - Fluidized Bed Combustion
        - Cyclone
        """
        try:
            boiler_type = enhanced_data.get("boiler_type", "")
            if not boiler_type or boiler_type in ["", "Not available", "default null"]:
                return False

            boiler_lower = str(boiler_type).lower().strip()

            # Comprehensive mapping for boiler type variations
            boiler_mapping = {
                # Mechanical Stoker-Fired variations
                "mechanical stoker-fired": "Mechanical Stoker-Fired",
                "mechanical stoker fired": "Mechanical Stoker-Fired",
                "stoker-fired": "Mechanical Stoker-Fired",
                "stoker fired": "Mechanical Stoker-Fired",
                "mechanical stoker": "Mechanical Stoker-Fired",
                "stoker": "Mechanical Stoker-Fired",
                "chain grate stoker": "Mechanical Stoker-Fired",
                "traveling grate": "Mechanical Stoker-Fired",
                "underfeed stoker": "Mechanical Stoker-Fired",

                # Pulverized Coal variations
                "pulverized coal": "Pulverized Coal",
                "pulverised coal": "Pulverized Coal",
                "pc": "Pulverized Coal",
                "pc fired": "Pulverized Coal",
                "pc-fired": "Pulverized Coal",
                "pulverized coal fired": "Pulverized Coal",
                "pulverised coal fired": "Pulverized Coal",
                "pulverized fuel": "Pulverized Coal",
                "pulverised fuel": "Pulverized Coal",
                "pf": "Pulverized Coal",
                "pf fired": "Pulverized Coal",
                "pf-fired": "Pulverized Coal",
                "tangentially fired": "Pulverized Coal",
                "wall fired": "Pulverized Coal",
                "corner fired": "Pulverized Coal",

                # Fluidized Bed Combustion variations
                "fluidized bed combustion": "Fluidized Bed Combustion",
                "fluidised bed combustion": "Fluidized Bed Combustion",
                "fbc": "Fluidized Bed Combustion",
                "cfbc": "Fluidized Bed Combustion",
                "circulating fluidized bed": "Fluidized Bed Combustion",
                "circulating fluidised bed": "Fluidized Bed Combustion",
                "atmospheric fluidized bed": "Fluidized Bed Combustion",
                "atmospheric fluidised bed": "Fluidized Bed Combustion",
                "afbc": "Fluidized Bed Combustion",
                "pfbc": "Fluidized Bed Combustion",
                "pressurized fluidized bed": "Fluidized Bed Combustion",
                "pressurised fluidised bed": "Fluidized Bed Combustion",
                "bubbling fluidized bed": "Fluidized Bed Combustion",
                "bubbling fluidised bed": "Fluidized Bed Combustion",
                "bfbc": "Fluidized Bed Combustion",

                # Cyclone variations
                "cyclone": "Cyclone",
                "cyclone fired": "Cyclone",
                "cyclone-fired": "Cyclone",
                "cyclone furnace": "Cyclone",
                "cyclone boiler": "Cyclone",
                "cyclone combustion": "Cyclone",
                "wet bottom cyclone": "Cyclone",
                "dry bottom cyclone": "Cyclone"
            }

            # Check if boiler_type contains coal-related terms OR boiler patterns
            # (standardize if it has coal terms OR if it matches boiler patterns)
            is_coal_plant = any(coal_term in boiler_lower for coal_term in [
                "coal", "lignite", "bituminous", "anthracite", "sub-bituminous"
            ])

            # Also standardize if it matches boiler patterns (likely coal plants)
            has_boiler_pattern = any(pattern in boiler_lower for pattern in [
                "stoker", "pulverized", "pulverised", "fluidized", "fluidised",
                "cyclone", "pc", "pf", "fbc", "cfbc", "afbc", "pfbc", "bfbc"
            ])

            if not is_coal_plant and not has_boiler_pattern:
                # For non-coal plants without boiler patterns, don't standardize
                print(f"[Session {session_id}] ℹ️ Boiler type not standardized (non-coal or unrecognized): '{boiler_type}'")
                return False

            # Find matching standardized term (prioritize longer patterns first)
            standardized_boiler = None
            # Sort by length descending to match longer patterns first
            sorted_mappings = sorted(boiler_mapping.items(), key=lambda x: len(x[0]), reverse=True)
            for variant, standard in sorted_mappings:
                if variant in boiler_lower:
                    standardized_boiler = standard
                    break

            if standardized_boiler and standardized_boiler != boiler_type:
                enhanced_data["boiler_type"] = standardized_boiler
                print(f"[Session {session_id}] 🔧 Boiler type standardized: '{boiler_type}' → '{standardized_boiler}'")
                return True
            elif standardized_boiler == boiler_type:
                print(f"[Session {session_id}] ✅ Boiler type already standardized: '{boiler_type}'")
                return False
            else:
                print(f"[Session {session_id}] ℹ️ Boiler type not standardized (no mapping found): '{boiler_type}'")
                return False

        except Exception as e:
            logger.error(f"[Session {session_id}] Error standardizing boiler_type field: {str(e)}")
            print(f"[Session {session_id}] ❌ BOILER TYPE STANDARDIZATION ERROR: {str(e)}")
            return False

    def _calculate_heat_rate_and_efficiency(self, enhanced_data: Dict, session_id: str, calculations_performed: List[str]) -> None:
        """
        Calculate heat_rate and coal_unit_efficiency using the formula:
        heat_rate = 859.85 / coal_unit_efficiency

        If we have heat_rate but not coal_unit_efficiency, calculate efficiency.
        If we have coal_unit_efficiency but not heat_rate, calculate heat_rate.
        """
        try:
            heat_rate = enhanced_data.get("heat_rate")
            coal_efficiency = enhanced_data.get("coal_unit_efficiency")

            # Convert string values to numeric if needed
            heat_rate_numeric = None
            efficiency_numeric = None

            if heat_rate and str(heat_rate).strip() not in ["", "Not available", "default null"]:
                try:
                    heat_rate_numeric = float(str(heat_rate).replace("%", "").strip())
                except (ValueError, TypeError):
                    heat_rate_numeric = None

            if coal_efficiency and str(coal_efficiency).strip() not in ["", "Not available", "default null"]:
                try:
                    efficiency_str = str(coal_efficiency).replace("%", "").strip()
                    efficiency_numeric = float(efficiency_str)
                    # If efficiency is in percentage format (>1), convert to decimal
                    if efficiency_numeric > 1:
                        efficiency_numeric = efficiency_numeric / 100
                except (ValueError, TypeError):
                    efficiency_numeric = None

            # Case 1: We have efficiency but not heat_rate - calculate heat_rate
            if efficiency_numeric and not heat_rate_numeric:
                calculated_heat_rate = 859.85 / efficiency_numeric
                enhanced_data["heat_rate"] = calculated_heat_rate
                enhanced_data["heat_rate_unit"] = "kJ/kWh"
                calculations_performed.append("Heat Rate")
                print(f"[Session {session_id}] 🔧 Calculated heat_rate: {calculated_heat_rate:.2f} kJ/kWh (from efficiency: {efficiency_numeric:.4f})")

            # Case 2: We have heat_rate but not efficiency - calculate efficiency
            elif heat_rate_numeric and not efficiency_numeric:
                calculated_efficiency = 859.85 / heat_rate_numeric
                enhanced_data["coal_unit_efficiency"] = calculated_efficiency
                calculations_performed.append("Coal Unit Efficiency")
                print(f"[Session {session_id}] 🔧 Calculated coal_unit_efficiency: {calculated_efficiency:.4f} (from heat_rate: {heat_rate_numeric} kJ/kWh)")

            # Case 3: We have both - verify they are consistent (optional validation)
            elif heat_rate_numeric and efficiency_numeric:
                expected_heat_rate = 859.85 / efficiency_numeric
                difference = abs(heat_rate_numeric - expected_heat_rate)
                if difference > 50:  # Allow some tolerance
                    print(f"[Session {session_id}] ⚠️ Heat rate and efficiency may be inconsistent:")
                    print(f"    Given heat_rate: {heat_rate_numeric} kJ/kWh")
                    print(f"    Given efficiency: {efficiency_numeric:.4f}")
                    print(f"    Expected heat_rate from efficiency: {expected_heat_rate:.2f} kJ/kWh")
                else:
                    print(f"[Session {session_id}] ✅ Heat rate and efficiency are consistent")

            # Case 4: We have neither - this is handled by other fallback calculations
            else:
                print(f"[Session {session_id}] ℹ️ Neither heat_rate nor coal_unit_efficiency available for calculation")

        except Exception as e:
            logger.error(f"[Session {session_id}] Error in heat_rate calculation: {str(e)}")
            print(f"[Session {session_id}] ❌ HEAT RATE CALCULATION ERROR: {str(e)}")

    def _normalize_efficiency_value(self, value, field: str, session_id: str):
        """
        Normalize efficiency values to 0.x decimal format (divide by 100 if percentage)

        Args:
            value: Input efficiency value (could be percentage or decimal)
            field: Field name for logging
            session_id: Session ID for logging

        Returns:
            Normalized efficiency value as decimal (0.x format) or None if invalid
        """
        try:
            if not value or value in ["", "default null", "Not available", "null"]:
                return None

            # Remove any non-numeric characters except decimal point and minus
            import re
            clean_value = re.sub(r'[^\d.-]', '', str(value))

            if not clean_value:
                return None

            numeric_value = float(clean_value)

            # If value is greater than 1, assume it's a percentage and divide by 100
            if numeric_value > 1:
                original_value = numeric_value
                numeric_value = numeric_value / 100
                print(f"[Session {session_id}] ⚡ Converted {field} from percentage: {original_value}% → {numeric_value:.4f}")
            else:
                print(f"[Session {session_id}] ✅ {field} already in decimal format: {numeric_value:.4f}")

            # Format to reasonable decimal places
            return round(numeric_value, 4)

        except (ValueError, TypeError) as e:
            logger.error(f"Error normalizing efficiency value for {field}: {str(e)}")
            return None


# Global fallback engine
FALLBACK_ENGINE = FallbackCalculationEngine()


def enhance_unit_with_calculations(extracted_data: Dict, unit_context: Dict, session_id: str = "unknown") -> Dict:
    """
    Convenience function to enhance unit data with fallback calculations
    
    Args:
        extracted_data: Data extracted from search
        unit_context: Unit context information
        session_id: Session identifier
        
    Returns:
        Enhanced data with calculated missing fields
    """
    return FALLBACK_ENGINE.enhance_unit_data(extracted_data, unit_context, session_id)