"""
DynamoDB Manager for storing organization data
"""

import boto3
import os
from typing import Dict, List, Optional


class DynamoDBManager:
    """Manager for DynamoDB operations"""
    
    def __init__(self):
        """Initialize DynamoDB client with credentials from .env"""
        self.dynamodb = boto3.resource(
            'dynamodb',
            region_name=os.getenv('AWS_REGION', 'ap-south-1'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
        )
        
        # Table name for organization data
        self.table_name = 'power-plant-organizations'
        self.table = None
        
        # Initialize table
        self._ensure_table_exists()
    
    def _ensure_table_exists(self):
        """Ensure the DynamoDB table exists, create if not"""
        try:
            # Try to get the table
            self.table = self.dynamodb.Table(self.table_name)
            self.table.load()  # This will raise an exception if table doesn't exist
            print(f"✅ DynamoDB table '{self.table_name}' found")
            
        except self.dynamodb.meta.client.exceptions.ResourceNotFoundException:
            print(f"🔧 Creating DynamoDB table '{self.table_name}'...")
            self._create_table()
        except Exception as e:
            print(f"❌ Error checking DynamoDB table: {e}")
            raise
    
    def _create_table(self):
        """Create the DynamoDB table for organization data"""
        try:
            table = self.dynamodb.create_table(
                TableName=self.table_name,
                KeySchema=[
                    {
                        'AttributeName': 'org_uid',
                        'KeyType': 'HASH'  # Partition key
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'org_uid',
                        'AttributeType': 'S'
                    }
                ],
                BillingMode='PAY_PER_REQUEST'  # On-demand billing
            )
            
            # Wait for table to be created
            table.wait_until_exists()
            self.table = table
            print(f"✅ DynamoDB table '{self.table_name}' created successfully")
            
        except Exception as e:
            print(f"❌ Error creating DynamoDB table: {e}")
            raise
    
    def save_organization_data(
        self,
        org_name: str,
        org_uid: str,
        country: str,
        plant_names: List[str]
    ) -> bool:
        """
        Save organization data to DynamoDB

        Args:
            org_name: Organization name
            org_uid: Organization UID (from API input)
            country: Country name
            plant_names: List of plant names owned by the organization

        Returns:
            True if successful, False otherwise
        """
        try:
            # Prepare the item to save (only essential fields)
            item = {
                'org_uid': org_uid,
                'organization_name': org_name,
                'country': country,
                'plant_names': plant_names
            }
            
            # Save to DynamoDB
            response = self.table.put_item(Item=item)
            
            print(f"✅ Saved organization data to DynamoDB:")
            print(f"   - Organization: {org_name}")
            print(f"   - Org UID: {org_uid}")
            print(f"   - Country: {country}")
            print(f"   - Plants: {plant_names}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error saving organization data to DynamoDB: {e}")
            return False
    
    def get_organization_data(self, org_uid: str) -> Optional[Dict]:
        """
        Get organization data from DynamoDB
        
        Args:
            org_uid: Organization UID
            
        Returns:
            Organization data dict or None if not found
        """
        try:
            response = self.table.get_item(Key={'org_uid': org_uid})
            
            if 'Item' in response:
                return response['Item']
            else:
                return None
                
        except Exception as e:
            print(f"❌ Error getting organization data from DynamoDB: {e}")
            return None
    
    def update_organization_data(
        self,
        org_uid: str,
        updates: Dict
    ) -> bool:
        """
        Update organization data in DynamoDB

        Args:
            org_uid: Organization UID
            updates: Dictionary of fields to update

        Returns:
            True if successful, False otherwise
        """
        try:
            # Build update expression
            update_expression = "SET "
            expression_attribute_values = {}
            
            for key, value in updates.items():
                update_expression += f"{key} = :{key}, "
                expression_attribute_values[f":{key}"] = value
            
            # Remove trailing comma and space
            update_expression = update_expression.rstrip(", ")
            
            # Update the item
            response = self.table.update_item(
                Key={'org_uid': org_uid},
                UpdateExpression=update_expression,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="UPDATED_NEW"
            )
            
            print(f"✅ Updated organization data in DynamoDB for UID: {org_uid}")
            return True

        except Exception as e:
            print(f"❌ Error updating organization data in DynamoDB: {e}")
            return False
    
    def list_organizations(self, limit: int = 100) -> List[Dict]:
        """
        List all organizations in DynamoDB
        
        Args:
            limit: Maximum number of organizations to return
            
        Returns:
            List of organization data dictionaries
        """
        try:
            response = self.table.scan(Limit=limit)
            return response.get('Items', [])
            
        except Exception as e:
            print(f"❌ Error listing organizations from DynamoDB: {e}")
            return []
    
    def delete_organization_data(self, org_uid: str) -> bool:
        """
        Delete organization data from DynamoDB

        Args:
            org_uid: Organization UID

        Returns:
            True if successful, False otherwise
        """
        try:
            response = self.table.delete_item(Key={'org_uid': org_uid})
            
            print(f"✅ Deleted organization data from DynamoDB for UID: {org_uid}")
            return True

        except Exception as e:
            print(f"❌ Error deleting organization data from DynamoDB: {e}")
            return False


# Global instance
_dynamodb_manager = None

def get_dynamodb_manager() -> DynamoDBManager:
    """Get the global DynamoDB manager instance"""
    global _dynamodb_manager
    if _dynamodb_manager is None:
        _dynamodb_manager = DynamoDBManager()
    return _dynamodb_manager
