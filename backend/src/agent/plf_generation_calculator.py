#!/usr/bin/env python3
"""
PLF, Generation Estimation & Net-to-Gross Conversion Calculator

This module implements comprehensive PLF calculations, unit-level generation estimation,
and gross generation conversion from net generation following the specified requirements.

Author: Augment Agent
Date: 2025-07-31
"""

import logging
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class GenerationData:
    """Data structure for generation information"""
    gross_plant_generation_mwh: Optional[float] = None
    net_generation_mwh: Optional[float] = None
    gross_unit_generation_mwh: Optional[float] = None
    plant_capacity_mw: Optional[float] = None
    unit_capacity_mw: Optional[float] = None
    unit_nameplate_capacity: Optional[float] = None
    plant_nameplate_capacity: Optional[float] = None
    paf_plant_level: float = 1.0  # Default to 100% availability
    paf_unit_level: float = 1.0   # Default to 100% availability
    aux_percentage: Optional[float] = None  # Auxiliary power percentage (decimal)


@dataclass
class PLFResults:
    """Results from PLF calculations"""
    plf_plant_level: Optional[float] = None
    plf_unit_level: Optional[float] = None
    generation_unit_level_mwh: Optional[float] = None
    gross_generation_mwh: Optional[float] = None
    calculation_method: str = ""
    aux_percentage_used: Optional[float] = None


class PLFGenerationCalculator:
    """
    Comprehensive PLF, Generation Estimation & Net-to-Gross Conversion Calculator
    
    Implements the four main calculation cases:
    1. Only Plant-Level Gross Generation Available
    2. Unit-Level Generation Available for All Units
    3. Partial Unit-Level Generation Available
    4. Only Net Generation Available
    """
    
    def __init__(self):
        """Initialize the calculator with standard auxiliary power lookup table"""
        # Standard auxiliary power consumption by technology and capacity
        self.aux_power_standards = {
            "subcritical": {
                "default": 0.08,  # 8%
                "ranges": {
                    (0, 200): 0.09,     # 9% for smaller plants
                    (200, 500): 0.08,   # 8% for medium plants
                    (500, 1000): 0.07,  # 7% for larger plants
                    (1000, float('inf')): 0.06  # 6% for very large plants
                }
            },
            "supercritical": {
                "default": 0.07,  # 7%
                "ranges": {
                    (0, 200): 0.08,
                    (200, 500): 0.07,
                    (500, 1000): 0.06,
                    (1000, float('inf')): 0.05
                }
            },
            "ultra_supercritical": {
                "default": 0.06,  # 6%
                "ranges": {
                    (0, 200): 0.07,
                    (200, 500): 0.06,
                    (500, 1000): 0.05,
                    (1000, float('inf')): 0.04
                }
            },
            "advanced_usc": {
                "default": 0.05,  # 5%
                "ranges": {
                    (0, 200): 0.06,
                    (200, 500): 0.05,
                    (500, 1000): 0.04,
                    (1000, float('inf')): 0.03
                }
            },
            "gas_turbine": {
                "default": 0.03,  # 3%
                "ranges": {
                    (0, 100): 0.04,
                    (100, 300): 0.03,
                    (300, float('inf')): 0.02
                }
            },
            "combined_cycle": {
                "default": 0.04,  # 4%
                "ranges": {
                    (0, 200): 0.05,
                    (200, 500): 0.04,
                    (500, float('inf')): 0.03
                }
            }
        }
        
        self.hours_per_year = 8760
    
    def get_auxiliary_power_percentage(self, capacity_mw: float, technology: str = "subcritical") -> float:
        """
        Get auxiliary power percentage based on capacity and technology
        
        Args:
            capacity_mw: Plant/unit capacity in MW
            technology: Technology type
            
        Returns:
            Auxiliary power percentage as decimal (e.g., 0.08 for 8%)
        """
        # Normalize technology name
        tech_normalized = technology.lower().replace("-", "_").replace(" ", "_")
        
        # Get technology standards
        tech_standards = self.aux_power_standards.get(tech_normalized)
        if not tech_standards:
            tech_standards = self.aux_power_standards["subcritical"]  # Default fallback
        
        # Check capacity ranges
        for (min_cap, max_cap), aux_percentage in tech_standards["ranges"].items():
            if min_cap <= capacity_mw < max_cap:
                return aux_percentage
        
        # Return default if no range matches
        return tech_standards["default"]
    
    def calculate_plf_plant_level(self, gross_plant_generation_mwh: float, 
                                 plant_capacity_mw: float, 
                                 paf_plant_level: float = 1.0) -> float:
        """
        Calculate PLF at plant level
        
        Formula: PLF_plant_level = gross_plant_generation_MWh / (plant_capacity_MW * 8760 * PAF_plant_level)
        
        Args:
            gross_plant_generation_mwh: Annual gross generation in MWh
            plant_capacity_mw: Plant capacity in MW
            paf_plant_level: Plant Availability Factor (decimal, default 1.0)
            
        Returns:
            PLF as decimal (e.g., 0.72 for 72%)
        """
        if plant_capacity_mw <= 0 or paf_plant_level <= 0:
            raise ValueError("Capacity and PAF must be positive values")
        
        max_generation_mwh = plant_capacity_mw * self.hours_per_year * paf_plant_level
        plf = gross_plant_generation_mwh / max_generation_mwh
        
        return round(plf, 4)
    
    def calculate_plf_unit_level(self, gross_unit_generation_mwh: float,
                                unit_capacity_mw: float,
                                paf_unit_level: float = 1.0) -> float:
        """
        Calculate PLF at unit level
        
        Formula: PLF_unit_level = gross_unit_generation_MWh / (unit_capacity_MW * 8760 * PAF_unit_level)
        
        Args:
            gross_unit_generation_mwh: Annual gross generation for unit in MWh
            unit_capacity_mw: Unit capacity in MW
            paf_unit_level: Unit Availability Factor (decimal, default 1.0)
            
        Returns:
            PLF as decimal (e.g., 0.72 for 72%)
        """
        if unit_capacity_mw <= 0 or paf_unit_level <= 0:
            raise ValueError("Capacity and PAF must be positive values")
        
        max_generation_mwh = unit_capacity_mw * self.hours_per_year * paf_unit_level
        plf = gross_unit_generation_mwh / max_generation_mwh
        
        return round(plf, 4)
    
    def estimate_unit_generation_from_plant(self, unit_capacity_mw: float,
                                          plf_plant_level: float,
                                          paf_plant_level: float = 1.0) -> float:
        """
        Estimate unit-level generation from plant-level PLF
        
        Formula: generation_unit_level_MWh = unit_capacity_MW * PLF_plant_level * PAF_plant_level * 8760
        
        Args:
            unit_capacity_mw: Unit capacity in MW
            plf_plant_level: Plant-level PLF (decimal)
            paf_plant_level: Plant-level PAF (decimal, default 1.0)
            
        Returns:
            Estimated unit generation in MWh
        """
        generation_mwh = unit_capacity_mw * plf_plant_level * paf_plant_level * self.hours_per_year
        return round(generation_mwh, 2)
    
    def estimate_unit_generation_by_capacity_ratio(self, gross_plant_generation_mwh: float,
                                                  unit_nameplate_capacity: float,
                                                  plant_nameplate_capacity: float) -> float:
        """
        Estimate unit generation based on capacity ratio
        
        Formula: estimated_gross_unit_generation = gross_plant_generation_MWh * (unit_nameplate_capacity / plant_nameplate_capacity)
        
        Args:
            gross_plant_generation_mwh: Total plant generation in MWh
            unit_nameplate_capacity: Unit nameplate capacity
            plant_nameplate_capacity: Plant nameplate capacity
            
        Returns:
            Estimated unit generation in MWh
        """
        if plant_nameplate_capacity <= 0:
            raise ValueError("Plant nameplate capacity must be positive")
        
        capacity_ratio = unit_nameplate_capacity / plant_nameplate_capacity
        estimated_generation = gross_plant_generation_mwh * capacity_ratio
        
        return round(estimated_generation, 2)
    
    def convert_net_to_gross_generation(self, net_generation_mwh: float,
                                       aux_percentage: Optional[float] = None,
                                       capacity_mw: Optional[float] = None,
                                       technology: str = "subcritical") -> tuple[float, float]:
        """
        Convert net generation to gross generation
        
        Formula: gross_generation_MWh = net_generation_MWh / (1 - AUX)
        
        Args:
            net_generation_mwh: Net generation in MWh
            aux_percentage: Auxiliary power percentage (decimal). If None, will estimate from capacity/technology
            capacity_mw: Capacity in MW (needed if aux_percentage is None)
            technology: Technology type (needed if aux_percentage is None)
            
        Returns:
            Tuple of (gross_generation_mwh, aux_percentage_used)
        """
        # Determine auxiliary power percentage
        if aux_percentage is None:
            if capacity_mw is None:
                raise ValueError("Either aux_percentage or capacity_mw must be provided")
            aux_percentage = self.get_auxiliary_power_percentage(capacity_mw, technology)
        
        # Validate aux_percentage
        if aux_percentage >= 1.0 or aux_percentage < 0:
            raise ValueError("Auxiliary power percentage must be between 0 and 1")
        
        # Calculate gross generation
        gross_generation_mwh = net_generation_mwh / (1 - aux_percentage)
        
        return round(gross_generation_mwh, 2), aux_percentage

    def calculate_case_1_plant_level_only(self, data: GenerationData) -> PLFResults:
        """
        Case 1: Only Plant-Level Gross Generation is Available

        Steps:
        1. Calculate PLF at plant level
        2. Estimate unit-level generation using plant PLF

        Args:
            data: GenerationData with gross_plant_generation_mwh, plant_capacity_mw, unit_capacity_mw

        Returns:
            PLFResults with calculated values
        """
        if not all([data.gross_plant_generation_mwh, data.plant_capacity_mw, data.unit_capacity_mw]):
            raise ValueError("Case 1 requires: gross_plant_generation_mwh, plant_capacity_mw, unit_capacity_mw")

        # Step 1: Calculate PLF at plant level
        plf_plant = self.calculate_plf_plant_level(
            data.gross_plant_generation_mwh,
            data.plant_capacity_mw,
            data.paf_plant_level
        )

        # Step 2: Estimate unit-level generation
        unit_generation = self.estimate_unit_generation_from_plant(
            data.unit_capacity_mw,
            plf_plant,
            data.paf_plant_level
        )

        return PLFResults(
            plf_plant_level=plf_plant,
            generation_unit_level_mwh=unit_generation,
            calculation_method="Case 1: Plant-level generation to unit estimation"
        )

    def calculate_case_2_unit_level_available(self, data: GenerationData) -> PLFResults:
        """
        Case 2: Unit-Level Generation is Available for All Units

        Steps:
        1. Calculate PLF at unit level

        Args:
            data: GenerationData with gross_unit_generation_mwh, unit_capacity_mw

        Returns:
            PLFResults with calculated values
        """
        if not all([data.gross_unit_generation_mwh, data.unit_capacity_mw]):
            raise ValueError("Case 2 requires: gross_unit_generation_mwh, unit_capacity_mw")

        # Calculate PLF at unit level
        plf_unit = self.calculate_plf_unit_level(
            data.gross_unit_generation_mwh,
            data.unit_capacity_mw,
            data.paf_unit_level
        )

        return PLFResults(
            plf_unit_level=plf_unit,
            generation_unit_level_mwh=data.gross_unit_generation_mwh,
            calculation_method="Case 2: Direct unit-level PLF calculation"
        )

    def calculate_case_3_partial_unit_data(self, data: GenerationData) -> PLFResults:
        """
        Case 3: Partial Unit-Level Generation Available

        For remaining units without generation data:
        estimated_gross_unit_generation = gross_plant_generation_MWh * (unit_nameplate_capacity / plant_nameplate_capacity)

        Args:
            data: GenerationData with gross_plant_generation_mwh, unit_nameplate_capacity, plant_nameplate_capacity

        Returns:
            PLFResults with calculated values
        """
        if not all([data.gross_plant_generation_mwh, data.unit_nameplate_capacity, data.plant_nameplate_capacity]):
            raise ValueError("Case 3 requires: gross_plant_generation_mwh, unit_nameplate_capacity, plant_nameplate_capacity")

        # Estimate unit generation by capacity ratio
        unit_generation = self.estimate_unit_generation_by_capacity_ratio(
            data.gross_plant_generation_mwh,
            data.unit_nameplate_capacity,
            data.plant_nameplate_capacity
        )

        # Calculate PLF if unit capacity is available
        plf_unit = None
        if data.unit_capacity_mw:
            plf_unit = self.calculate_plf_unit_level(
                unit_generation,
                data.unit_capacity_mw,
                data.paf_unit_level
            )

        return PLFResults(
            plf_unit_level=plf_unit,
            generation_unit_level_mwh=unit_generation,
            calculation_method="Case 3: Capacity ratio estimation for partial data"
        )

    def calculate_case_4_net_generation_only(self, data: GenerationData,
                                           capacity_mw: Optional[float] = None,
                                           technology: str = "subcritical") -> PLFResults:
        """
        Case 4: Only Net Generation is Available

        Case 4a: If AUX% is available, use it directly
        Case 4b: If AUX% is not available, estimate from lookup table

        Formula: gross_generation_MWh = net_generation_MWh / (1 - AUX)

        Args:
            data: GenerationData with net_generation_mwh
            capacity_mw: Capacity for AUX estimation (if data.aux_percentage is None)
            technology: Technology for AUX estimation (if data.aux_percentage is None)

        Returns:
            PLFResults with calculated values
        """
        if not data.net_generation_mwh:
            raise ValueError("Case 4 requires: net_generation_mwh")

        # Use provided capacity or fallback to data capacity
        if capacity_mw is None:
            capacity_mw = data.unit_capacity_mw or data.plant_capacity_mw

        # Convert net to gross generation
        gross_generation, aux_used = self.convert_net_to_gross_generation(
            data.net_generation_mwh,
            data.aux_percentage,
            capacity_mw,
            technology
        )

        # Calculate PLF if capacity is available
        plf_result = None
        if data.unit_capacity_mw:
            plf_result = self.calculate_plf_unit_level(
                gross_generation,
                data.unit_capacity_mw,
                data.paf_unit_level
            )
        elif data.plant_capacity_mw:
            plf_result = self.calculate_plf_plant_level(
                gross_generation,
                data.plant_capacity_mw,
                data.paf_plant_level
            )

        method = f"Case 4{'a' if data.aux_percentage else 'b'}: Net to gross conversion"
        if data.aux_percentage:
            method += f" (provided AUX: {aux_used*100:.1f}%)"
        else:
            method += f" (estimated AUX: {aux_used*100:.1f}%)"

        return PLFResults(
            plf_unit_level=plf_result if data.unit_capacity_mw else None,
            plf_plant_level=plf_result if data.plant_capacity_mw and not data.unit_capacity_mw else None,
            gross_generation_mwh=gross_generation,
            calculation_method=method,
            aux_percentage_used=aux_used
        )

    def calculate_comprehensive_plf(self, data: GenerationData,
                                   technology: str = "subcritical",
                                   auto_detect_case: bool = True) -> PLFResults:
        """
        Main method to calculate PLF using the most appropriate case based on available data

        Args:
            data: GenerationData with available information
            technology: Technology type for auxiliary power estimation
            auto_detect_case: If True, automatically detect which case to use

        Returns:
            PLFResults with calculated values and method used
        """
        if auto_detect_case:
            # Auto-detect which case to use based on available data

            # Case 2: Unit-level generation available
            if data.gross_unit_generation_mwh and data.unit_capacity_mw:
                return self.calculate_case_2_unit_level_available(data)

            # Case 1: Only plant-level generation available
            elif (data.gross_plant_generation_mwh and
                  data.plant_capacity_mw and
                  data.unit_capacity_mw):
                return self.calculate_case_1_plant_level_only(data)

            # Case 3: Partial unit data (for capacity ratio estimation)
            elif (data.gross_plant_generation_mwh and
                  data.unit_nameplate_capacity and
                  data.plant_nameplate_capacity):
                return self.calculate_case_3_partial_unit_data(data)

            # Case 4: Only net generation available
            elif data.net_generation_mwh:
                capacity_mw = data.unit_capacity_mw or data.plant_capacity_mw
                return self.calculate_case_4_net_generation_only(data, capacity_mw, technology)

            else:
                raise ValueError("Insufficient data for any calculation case. Need at least one of: "
                               "gross_unit_generation_mwh, gross_plant_generation_mwh, or net_generation_mwh")

        else:
            # Manual case selection would go here if needed
            raise NotImplementedError("Manual case selection not implemented. Use auto_detect_case=True")

    def format_plf_for_output(self, plf_decimal: float, as_percentage: bool = True) -> Union[float, str]:
        """
        Format PLF value for output

        Args:
            plf_decimal: PLF as decimal (e.g., 0.72)
            as_percentage: If True, return as percentage string (e.g., "72.0%")

        Returns:
            Formatted PLF value
        """
        if as_percentage:
            return f"{plf_decimal * 100:.1f}%"
        else:
            return round(plf_decimal, 4)

    def validate_inputs(self, data: GenerationData) -> List[str]:
        """
        Validate input data and return list of validation errors

        Args:
            data: GenerationData to validate

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        # Check for negative values
        numeric_fields = [
            ('gross_plant_generation_mwh', data.gross_plant_generation_mwh),
            ('net_generation_mwh', data.net_generation_mwh),
            ('gross_unit_generation_mwh', data.gross_unit_generation_mwh),
            ('plant_capacity_mw', data.plant_capacity_mw),
            ('unit_capacity_mw', data.unit_capacity_mw),
            ('unit_nameplate_capacity', data.unit_nameplate_capacity),
            ('plant_nameplate_capacity', data.plant_nameplate_capacity)
        ]

        for field_name, value in numeric_fields:
            if value is not None and value < 0:
                errors.append(f"{field_name} cannot be negative: {value}")

        # Check PAF values (should be between 0 and 1)
        if not (0 < data.paf_plant_level <= 1):
            errors.append(f"paf_plant_level must be between 0 and 1: {data.paf_plant_level}")

        if not (0 < data.paf_unit_level <= 1):
            errors.append(f"paf_unit_level must be between 0 and 1: {data.paf_unit_level}")

        # Check auxiliary power percentage
        if data.aux_percentage is not None and not (0 <= data.aux_percentage < 1):
            errors.append(f"aux_percentage must be between 0 and 1: {data.aux_percentage}")

        return errors


# Global instance for easy access
PLF_CALCULATOR = PLFGenerationCalculator()


def calculate_plf_comprehensive(gross_plant_generation_mwh: Optional[float] = None,
                               net_generation_mwh: Optional[float] = None,
                               gross_unit_generation_mwh: Optional[float] = None,
                               plant_capacity_mw: Optional[float] = None,
                               unit_capacity_mw: Optional[float] = None,
                               unit_nameplate_capacity: Optional[float] = None,
                               plant_nameplate_capacity: Optional[float] = None,
                               paf_plant_level: float = 1.0,
                               paf_unit_level: float = 1.0,
                               aux_percentage: Optional[float] = None,
                               technology: str = "subcritical") -> PLFResults:
    """
    Convenience function for comprehensive PLF calculation

    This function automatically detects the appropriate calculation case based on available data
    and returns comprehensive results including PLF, generation estimates, and conversion details.

    Args:
        gross_plant_generation_mwh: Total plant gross generation in MWh
        net_generation_mwh: Net generation in MWh (plant or unit level)
        gross_unit_generation_mwh: Unit gross generation in MWh
        plant_capacity_mw: Plant capacity in MW
        unit_capacity_mw: Unit capacity in MW
        unit_nameplate_capacity: Unit nameplate capacity
        plant_nameplate_capacity: Plant nameplate capacity
        paf_plant_level: Plant Availability Factor (decimal, default 1.0)
        paf_unit_level: Unit Availability Factor (decimal, default 1.0)
        aux_percentage: Auxiliary power percentage (decimal, optional)
        technology: Technology type for auxiliary power estimation

    Returns:
        PLFResults with calculated values and method used

    Example:
        >>> result = calculate_plf_comprehensive(
        ...     gross_plant_generation_mwh=3500000,
        ...     plant_capacity_mw=660,
        ...     unit_capacity_mw=330,
        ...     technology="supercritical"
        ... )
        >>> print(f"Plant PLF: {result.plf_plant_level*100:.1f}%")
        >>> print(f"Unit Generation: {result.generation_unit_level_mwh:,.0f} MWh")
    """
    # Create GenerationData object
    data = GenerationData(
        gross_plant_generation_mwh=gross_plant_generation_mwh,
        net_generation_mwh=net_generation_mwh,
        gross_unit_generation_mwh=gross_unit_generation_mwh,
        plant_capacity_mw=plant_capacity_mw,
        unit_capacity_mw=unit_capacity_mw,
        unit_nameplate_capacity=unit_nameplate_capacity,
        plant_nameplate_capacity=plant_nameplate_capacity,
        paf_plant_level=paf_plant_level,
        paf_unit_level=paf_unit_level,
        aux_percentage=aux_percentage
    )

    # Validate inputs
    errors = PLF_CALCULATOR.validate_inputs(data)
    if errors:
        raise ValueError(f"Input validation failed: {'; '.join(errors)}")

    # Calculate using comprehensive method
    return PLF_CALCULATOR.calculate_comprehensive_plf(data, technology)
