"""
JSON S3 Storage Module for Power Plant Research Data

This module handles uploading JSON data at different processing levels
(organization, plant, unit) to S3 with proper folder structure.
"""

import os
import json
import re
import boto3
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# S3 Configuration - Use S3-specific credentials
S3_AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
S3_AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-plants'

print(f"🔧 S3 Configuration:")
print(f"   - S3 Access Key: {S3_AWS_ACCESS_KEY[:8] + '...' if S3_AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")

def sanitize_plant_name(plant_name: str) -> str:
    """
    Convert power plant name to S3-safe folder name.
    
    Examples:
        "Jhajjar Power Plant" → "Jhajjar_Power_Plant"
        "NTPC Dadri (Stage-II)" → "NTPC_Dadri_Stage_II"
        "Adani Mundra Power Station" → "Adani_Mundra_Power_Station"
    """
    if not plant_name:
        return "Unknown_Plant"
    
    # Remove special characters except spaces and hyphens, replace & with _and_
    cleaned = plant_name.replace('&', '_and_')
    cleaned = re.sub(r'[^\w\s-]', '', cleaned)
    
    # Replace spaces and hyphens with underscores
    sanitized = cleaned.replace(' ', '_').replace('-', '_')
    
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    return sanitized if sanitized else "Unknown_Plant"

def get_plant_type_from_plant_json(plant_uid: str, org_id: str, session_id: str) -> str:
    """
    Get plant_type from the existing plant-level JSON in S3
    Since unit JSONs are generated after plant JSONs, the plant JSON already exists

    Args:
        plant_uid: Plant UUID (folder name)
        org_id: Organization ID
        session_id: Session ID for logging

    Returns:
        Plant type (e.g., 'coal', 'natural gas', etc.), defaults to 'coal'
    """
    try:
        import boto3
        import json

        # Get country from database for S3 path
        country_folder = "Unknown"
        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            # We need plant name to get country, but we can try common patterns
            # For now, we'll try to list files in the plant folder
        except:
            pass

        # Try to find and read the plant JSON file
        s3_client = boto3.client('s3')
        bucket_name = 'clem-transition-plants'

        # List files in the plant folder to find the plant JSON
        prefix = f"ZAM/{org_id}/{plant_uid}/"  # Assuming ZAM for now, could be improved

        try:
            response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)

            if 'Contents' in response:
                # Look for plant JSON file (starts with "plant#")
                for obj in response['Contents']:
                    key = obj['Key']
                    filename = key.split('/')[-1]  # Get just the filename

                    if filename.startswith('plant#') and filename.endswith('.json'):
                        # Found the plant JSON, read it
                        print(f"[Session {session_id}] 🔍 Found plant JSON: {filename}")

                        # Download and parse the plant JSON
                        response = s3_client.get_object(Bucket=bucket_name, Key=key)
                        plant_json_content = response['Body'].read().decode('utf-8')
                        plant_data = json.loads(plant_json_content)

                        # Get plant_type from the plant JSON
                        plant_type = plant_data.get('plant_type', '')
                        if plant_type:
                            print(f"[Session {session_id}] ✅ Retrieved plant_type from plant JSON: {plant_type}")
                            return plant_type.lower()

                        # Fallback: try technology field
                        technology = plant_data.get('technology', '')
                        if technology:
                            print(f"[Session {session_id}] 🔍 Using technology as plant_type: {technology}")
                            return technology.lower()

        except Exception as s3_error:
            print(f"[Session {session_id}] ⚠️ Could not read plant JSON from S3: {s3_error}")

        # Default fallback
        print(f"[Session {session_id}] ⚠️ Could not get plant_type from plant JSON, using default: coal")
        return "coal"

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error getting plant_type from plant JSON: {str(e)}")
        return "coal"

def get_sequential_plant_id_from_plant_json(plant_name: str, plant_uid: str, session_id: str) -> int:
    """
    Get sequential plant_id from plant-level JSON or derive from plant name

    Args:
        plant_name: Plant name to search for
        plant_uid: Plant UUID (folder name)
        session_id: Session ID for logging

    Returns:
        Sequential plant_id (1, 2, 3, etc.), defaults to 1
    """
    try:
        # For now, we'll use a simple approach to derive sequential plant_id
        # This could be enhanced later to actually read from the plant JSON in S3

        # Method 1: Try to extract from plant name patterns
        # Many plants have "Unit 1", "Unit 2" patterns or similar
        import re

        # Look for number patterns in plant name
        number_matches = re.findall(r'\b(\d+)\b', plant_name)
        if number_matches:
            # Use the first number found as a hint for plant numbering
            first_number = int(number_matches[0])
            if first_number > 0 and first_number <= 10:  # Reasonable range for plant numbering
                print(f"[Session {session_id}] 🔍 Derived sequential plant_id from name '{plant_name}': {first_number}")
                return first_number

        # Method 2: Try database lookup for additional context
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        existing_plant = db_manager.check_plant_exists(plant_name)
        if existing_plant:
            # Check if there's any sequential information in the database
            # For now, default to 1 but this could be enhanced
            sequential_plant_id = 1
            print(f"[Session {session_id}] 🔍 Using default sequential plant_id for {plant_name}: {sequential_plant_id}")
            return sequential_plant_id

        # Method 3: Default fallback
        print(f"[Session {session_id}] 🔍 Using fallback sequential plant_id: 1")
        return 1

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error getting sequential plant_id: {str(e)}")
        return 1

def get_country_folder_name(country: str) -> str:
    """Convert country name to 3-digit ISO country code for S3 folder name"""
    if not country or country.lower() in ['unknown', 'not available', '']:
        return "UNK"

    # Country name to ISO 3-digit code mapping
    country_codes = {
        'india': 'IND',
        'united states': 'USA',
        'united states of america': 'USA',
        'china': 'CHN',
        'japan': 'JPN',
        'germany': 'DEU',
        'united kingdom': 'GBR',
        'france': 'FRA',
        'italy': 'ITA',
        'canada': 'CAN',
        'australia': 'AUS',
        'brazil': 'BRA',
        'russia': 'RUS',
        'south korea': 'KOR',
        'mexico': 'MEX',
        'indonesia': 'IDN',
        'turkey': 'TUR',
        'saudi arabia': 'SAU',
        'south africa': 'ZAF',
        'argentina': 'ARG',
        'thailand': 'THA',
        'egypt': 'EGY',
        'nigeria': 'NGA',
        'bangladesh': 'BGD',
        'vietnam': 'VNM',
        'philippines': 'PHL',
        'malaysia': 'MYS',
        'singapore': 'SGP',
        'chile': 'CHL',
        'poland': 'POL',
        'ukraine': 'UKR',
        'pakistan': 'PAK',
        'sri lanka': 'LKA',
        'myanmar': 'MMR',
        'nepal': 'NPL',
        'bhutan': 'BTN',
        'maldives': 'MDV',
        'afghanistan': 'AFG',
        'iran': 'IRN',
        'iraq': 'IRQ',
        'israel': 'ISR',
        'jordan': 'JOR',
        'lebanon': 'LBN',
        'syria': 'SYR',
        'yemen': 'YEM',
        'oman': 'OMN',
        'qatar': 'QAT',
        'kuwait': 'KWT',
        'bahrain': 'BHR',
        'united arab emirates': 'ARE',
    }

    # Normalize country name for lookup
    country_normalized = country.lower().strip()

    # Direct lookup
    if country_normalized in country_codes:
        return country_codes[country_normalized]

    # Try partial matches for common variations
    for country_name, code in country_codes.items():
        if country_name in country_normalized or country_normalized in country_name:
            return code

    # If no match found, create a 3-letter code from the country name
    # Take first 3 letters and uppercase
    fallback_code = ''.join(c for c in country.upper() if c.isalpha())[:3]
    return fallback_code if len(fallback_code) == 3 else "UNK"

def upload_hierarchical_json_to_s3(
    json_data: Dict[Any, Any],
    country: str,
    org_id: str,
    plant_id: str,
    filename: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Upload JSON data to S3 with hierarchical folder structure.

    Structure: Country/OrgUID/PlantUID/filename.json

    Args:
        json_data: Dictionary containing the data to upload
        country: Country name for top-level folder
        org_id: Organization UID for second-level folder
        plant_id: Plant UID for third-level folder (empty for org-level files)
        filename: Name of the JSON file
        session_id: Session ID for logging

    Returns:
        S3 URL of uploaded file, or None if upload failed
    """
    try:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )

        # Add metadata to JSON
        enhanced_data = {
            **json_data,
            "metadata": {
                "uploaded_at": datetime.utcnow().isoformat() + "Z",
                "session_id": session_id,
                "country": country,
                "org_id": org_id,
                "plant_id": plant_id,
                "file_type": filename.replace('.json', ''),
                "bucket": S3_BUCKET
            }
        }

        # Convert to JSON string
        json_string = json.dumps(enhanced_data, indent=2, ensure_ascii=False)

        # Create hierarchical S3 key path
        country_folder = get_country_folder_name(country)

        if plant_id:
            # Plant/Unit/Transition level: Country/OrgUID/PlantUID/filename.json
            s3_key = f"{country_folder}/{org_id}/{plant_id}/{filename}"
        else:
            # Organization level: Country/OrgUID/OrgUID.json
            s3_key = f"{country_folder}/{org_id}/{filename}"

        # Upload to S3
        s3_client.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json_string,
            ContentType='application/json',
            ContentEncoding='utf-8'
        )

        # Generate S3 URL
        s3_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{s3_key}"

        print(f"[Session {session_id}] ✅ Hierarchical JSON uploaded: {s3_url}")
        print(f"[Session {session_id}] 📁 S3 Path: {s3_key}")
        return s3_url

    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to upload hierarchical {filename}: {str(e)}")
        return None

def store_organization_data(
    org_data: Dict[Any, Any], 
    plant_name: str, 
    session_id: str = "unknown",
    org_id: str = None
) -> Optional[str]:
    """
    Store organization-level data to S3.
    
    Args:
        org_data: Organization data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_id: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Add UID to organization data as primary key
    print(f"[Session {session_id}] 🔍 DEBUG: org_id = '{org_id}'")
    print(f"[Session {session_id}] 🔍 DEBUG: org_data keys = {list(org_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{org_data.get('pk', 'NOT_FOUND')}'")
    
    if org_id:
        org_data["org_id"] = org_id  # Changed from org_id to org_id
        # ALWAYS replace pk field with actual UID regardless of current value
        old_pk = org_data.get("pk", "NOT_FOUND")
        org_data["pk"] = org_id
        print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{org_id}'")
        print(f"[Session {session_id}] 🔑 Added org_id to organization data: {org_id}")
    else:
        print(f"[Session {session_id}] ❌ No org_id provided to store_organization_data")
        print(f"[Session {session_id}] 🔍 EMERGENCY FIX: Attempting to generate UID from org data...")

        # EMERGENCY FIX: Try to generate UID from organization data if not provided
        org_name = org_data.get("organization_name", "")
        country = org_data.get("country_name", "")

        if org_name and country:
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()
                emergency_uid = db_manager.generate_org_id(org_name, country)

                org_data["org_id"] = emergency_uid
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = emergency_uid
                print(f"[Session {session_id}] 🚨 EMERGENCY UID GENERATED: '{emergency_uid}'")
                print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{emergency_uid}'")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Emergency UID generation failed: {e}")
                # Last resort: set pk to null instead of "default null"
                org_data["pk"] = None
                print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
        else:
            print(f"[Session {session_id}] ❌ Cannot generate emergency UID: missing org_name or country")
            # Last resort: set pk to null instead of "default null"
            org_data["pk"] = None
            print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
    
    # CRITICAL FIX: Enforce fixed values for off_peak_hours and peak_hours right before storage
    # This ensures they are NEVER null regardless of what the LLM generated
    org_data["off_peak_hours"] = 0.466
    org_data["peak_hours"] = 0.9
    print(f"[Session {session_id}] 🔧 FINAL ENFORCEMENT: off_peak_hours=0.466, peak_hours=0.9")

    # Get country from organization data or database
    country = org_data.get("country_name", "Unknown")
    if country in ["Unknown", "Not available", "", None]:
        # Try to get country from database using plant name
        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            plant_info = db_manager.check_plant_exists(plant_name)
            if plant_info and plant_info.get("country"):
                country = plant_info["country"]
                print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")
            country = "Unknown"

    # Get org_id for filename and folder structure - USE PARAMETER FIRST
    if org_id:
        final_org_id = org_id
        print(f"[Session {session_id}] ✅ Using org_id from parameter: {final_org_id}")
    else:
        final_org_id = org_data.get("org_id", "UNKNOWN_ORG")
        print(f"[Session {session_id}] ⚠️ Using org_id from data (fallback): {final_org_id}")

    filename = "org_details.json"  # Static filename as requested

    print(f"[Session {session_id}] 🏢 Storing organization data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {final_org_id}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(org_data, country, final_org_id, "", filename, session_id)

def store_plant_data(
    plant_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown",
    org_id: str = None,
    plant_id: str = None
) -> Optional[str]:
    """
    Store plant-level data to S3.

    Args:
        plant_data: Plant data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_id: Organization UID
        plant_id: Plant UID (primary key for plant level)

    Returns:
        S3 URL of uploaded file
    """
    # Add UIDs to plant data - pk should be plant_id, not org_id
    print(f"[Session {session_id}] 🔍 DEBUG: org_id = '{org_id}'")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_id = '{plant_id}'")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_data keys = {list(plant_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{plant_data.get('pk', 'NOT_FOUND')}'")

    # Set org_id for reference
    if org_id:
        plant_data["org_id"] = org_id  # Changed from org_id to org_id
        print(f"[Session {session_id}] 🔑 Added org_id to plant data: {org_id}")

    # FIELD NAMING UPDATE: Change plant_id to plant_uid in plant JSON
    if plant_id:
        old_pk = plant_data.get("pk", "NOT_FOUND")
        plant_data["pk"] = plant_id

        # Extract sequential plant number from existing plant_id field (1, 2, 3, etc.)
        sequential_plant_id = plant_data.get("plant_id", 1)

        # Set new field structure for plant JSON
        plant_data["plant_uid"] = plant_id  # UUID from database (renamed from plant_id)
        plant_data["plant_id"] = sequential_plant_id  # Sequential number (1, 2, 3, etc.)

        print(f"[Session {session_id}] ✅ Set pk field to plant_uid: '{old_pk}' → '{plant_id}'")
        print(f"[Session {session_id}] 🔑 Added plant_uid (UUID): {plant_id}")
        print(f"[Session {session_id}] 🔢 Added plant_id (sequential): {sequential_plant_id}")
    elif org_id:
        # Fallback to org_id if plant_uid not available
        old_pk = plant_data.get("pk", "NOT_FOUND")
        plant_data["pk"] = org_id
        print(f"[Session {session_id}] ⚠️ No plant_uid, using org_id as pk: '{old_pk}' → '{org_id}'")
    else:
        print(f"[Session {session_id}] ❌ No plant_uid or org_id provided to store_plant_data")
    
    # Get country from database
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")

    # Generate filename from SK value
    sk_value = plant_data.get("sk", "plant_unknown")
    filename = f"{sk_value}.json"

    # Ensure org_id is not None or empty
    if not org_id:  # Handles both None and empty string
        org_id = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_id was None/empty, using fallback: {org_id}")

    if not plant_id:  # Handles both None and empty string
        plant_id = "UNKNOWN_PLANT"
        print(f"[Session {session_id}] ⚠️ plant_id was None/empty, using fallback: {plant_id}")

    print(f"[Session {session_id}] 🏭 Storing plant data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_id}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_id}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(plant_data, country, org_id, plant_id, filename, session_id)

def store_unit_data(
    unit_data: Dict[Any, Any],
    plant_name: str,
    unit_number: str,
    session_id: str = "unknown",
    org_id: str = None,
    plant_id: str = None
) -> Optional[str]:
    """
    Store individual unit data to S3.

    Args:
        unit_data: Unit data dictionary
        plant_name: Original plant name from user query
        unit_number: Unit number (e.g., "1", "2", "3")
        session_id: Session ID for tracking
        org_id: Organization UID
        plant_id: Plant UID (primary key for unit level)

    Returns:
        S3 URL of uploaded file
    """
    # Add UIDs to unit data - pk should be plant_id, not org_id
    print(f"[Session {session_id}] 🔍 DEBUG Unit {unit_number}: org_id = '{org_id}'")
    print(f"[Session {session_id}] 🔍 DEBUG Unit {unit_number}: plant_id = '{plant_id}'")

    # Set org_id for reference
    if org_id:
        unit_data["org_id"] = org_id  # Changed from org_id to org_id
        print(f"[Session {session_id}] 🔑 Added org_id to Unit {unit_number} data: {org_id}")

    # NOTE: Field population moved to after database lookup to ensure correct UUID is used
    
    # Get country from database
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")

    # Generate filename from SK value (FIXED: Use SK directly as filename)
    sk_value = unit_data.get("sk", f"unit_{unit_number}_unknown")
    filename = f"{sk_value}.json"  # FIXED: Remove redundant "unit_{unit_number}_" prefix

    # Ensure org_id and plant_id are not None or empty
    if not org_id:  # Handles both None and empty string
        org_id = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_id was None/empty, using fallback: {org_id}")

    if not plant_id:  # Handles both None and empty string
        # CRITICAL FIX: Get plant_uid from database as fallback
        print(f"[Session {session_id}] ⚠️ plant_id was None/empty, attempting database lookup...")

        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()

            # Get plant_id (UUID) from database using plant name
            existing_plant = db_manager.check_plant_exists(plant_name)
            if existing_plant:
                plant_id = existing_plant.get("plant_id", "")
                if plant_id:
                    print(f"[Session {session_id}] ✅ Retrieved plant_uid from database: {plant_id}")
                else:
                    plant_id = "UNKNOWN_PLANT"
                    print(f"[Session {session_id}] ⚠️ Database plant record has no plant_id, using fallback: {plant_id}")
            else:
                plant_id = "UNKNOWN_PLANT"
                print(f"[Session {session_id}] ⚠️ Plant not found in database: {plant_name}, using fallback: {plant_id}")

        except Exception as e:
            plant_id = "UNKNOWN_PLANT"
            print(f"[Session {session_id}] ❌ Database lookup failed: {str(e)}, using fallback: {plant_id}")

    # FIELD POPULATION: Now populate unit JSON fields with correct plant_uid from database
    if plant_id and plant_id != "UNKNOWN_PLANT":
        old_pk = unit_data.get("pk", "NOT_FOUND")

        # Get sequential plant_id from plant name/JSON
        sequential_plant_id = get_sequential_plant_id_from_plant_json(plant_name, plant_id, session_id)

        # Check if SK already has correct format (generated in unit extraction stages)
        old_sk = unit_data.get("sk", "")
        print(f"[Session {session_id}] 🔍 Unit SK from extraction: {old_sk}")

        # Only regenerate SK if it's missing or has wrong format
        if not old_sk or not old_sk.startswith("unit#"):
            # Fallback: regenerate SK with basic plant_type
            fallback_plant_type = "coal"  # Default fallback
            new_sk = f"unit#{fallback_plant_type}#{unit_number}#plant#{sequential_plant_id}"
            unit_data["sk"] = new_sk
            print(f"[Session {session_id}] 🔧 Regenerated SK: '{old_sk}' → '{new_sk}'")
        else:
            print(f"[Session {session_id}] ✅ Using existing SK from extraction: {old_sk}")

        # Set unit JSON fields with UUID from database
        unit_data["pk"] = plant_id  # UUID from database (primary key)
        unit_data["plant_uid"] = plant_id  # UUID from database (same as pk)
        unit_data["plant_id"] = sequential_plant_id  # Sequential number (1, 2, 3, etc.)

        print(f"[Session {session_id}] ✅ Set pk field to plant_uid: '{old_pk}' → '{plant_id}'")
        print(f"[Session {session_id}] 🔑 Added plant_uid (UUID) to Unit {unit_number}: {plant_id}")
        print(f"[Session {session_id}] 🔢 Added plant_id (sequential) to Unit {unit_number}: {sequential_plant_id}")
        # SK handling is now done above
    elif org_id:
        # Fallback to org_id if plant_uid not available
        old_pk = unit_data.get("pk", "NOT_FOUND")
        unit_data["pk"] = org_id
        unit_data["plant_uid"] = ""  # Empty if no plant_uid available
        unit_data["plant_id"] = 1  # Default sequential number
        print(f"[Session {session_id}] ⚠️ No plant_uid, using org_id as pk: '{old_pk}' → '{org_id}'")
    else:
        print(f"[Session {session_id}] ❌ No plant_uid or org_id provided to store_unit_data")

    print(f"[Session {session_id}] ⚡ Storing Unit {unit_number} data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_id}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_id}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(unit_data, country, org_id, plant_id, filename, session_id)

def store_transition_plan_data(
    plant_name: str,
    session_id: str = "unknown",
    org_id: str = None,
    plant_id: str = None
) -> Optional[str]:
    """
    Store Level-4 transition plan data to S3.

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_id: Organization UID (for reference)
        plant_id: Plant UID (primary key)

    Returns:
        S3 URL of uploaded file
    """
    # CRITICAL FIX: Use plant_id as pk, not org_id
    pk_value = plant_id if plant_id else org_id if org_id else ""

    # Create Level-4 transition plan JSON structure
    transition_plan_data = {
        "pk": pk_value,
        "sk": "transition_plan",
        "selected_plan_id": "",
        "transitionPlanStratName": "",
        "plant_id": plant_id if plant_id else "",
        "org_id": org_id if org_id else ""
    }

    print(f"[Session {session_id}] 🔍 DEBUG: Creating Level-4 transition plan")
    print(f"[Session {session_id}] 🔍 DEBUG: org_id = '{org_id}', plant_id = '{plant_id}'")
    print(f"[Session {session_id}] 🔍 DEBUG: pk_value = '{pk_value}'")
    print(f"[Session {session_id}] 🔍 DEBUG: transition_plan_data = {transition_plan_data}")

    if plant_id:
        print(f"[Session {session_id}] 🔑 Added plant_id as pk to transition plan: {plant_id}")
    elif org_id:
        print(f"[Session {session_id}] ⚠️ Using org_id as fallback pk: {org_id}")
    else:
        print(f"[Session {session_id}] ❌ No plant_id or org_id provided to store_transition_plan_data")

    # Get country from database
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")

    filename = "transition_plan.json"

    # Ensure org_id and plant_id are not None or empty
    if not org_id:  # Handles both None and empty string
        org_id = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_id was None/empty, using fallback: {org_id}")

    if not plant_id:  # Handles both None and empty string
        plant_id = "UNKNOWN_PLANT"
        print(f"[Session {session_id}] ⚠️ plant_id was None/empty, using fallback: {plant_id}")

    print(f"[Session {session_id}] 📋 Storing Level-4 transition plan hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_id}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_id}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    # Store transition_plan.json at organization level (with org_details.json), not inside plant folder
    return upload_hierarchical_json_to_s3(transition_plan_data, country, org_id, "", filename, session_id)

def get_plant_s3_urls(plant_name: str, session_id: str = "unknown") -> Dict[str, Any]:
    """
    Generate hierarchical S3 URLs for all expected files of a plant (for state tracking).

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        Dictionary with hierarchical URL structure for state management
    """
    # Get plant metadata from database for hierarchical structure
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)

        if plant_info:
            country = get_country_folder_name(plant_info.get("country", "Unknown"))
            org_id = plant_info.get("org_id", "UNKNOWN_ORG")
            plant_id = plant_info.get("plant_id", "UNKNOWN_PLANT")

            base_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{country}/{org_id}"
            plant_base_url = f"{base_url}/{plant_id}"

            return {
                "plant_name": plant_name,
                "country": country,
                "org_id": org_id,
                "plant_id": plant_id,
                "base_url": base_url,
                "plant_base_url": plant_base_url,
                "organization": f"{base_url}/org_details.json",  # Static filename
                "plant": f"{plant_base_url}/plant_sk.json",  # Will be updated with actual SK
                "units": {},  # Will be populated as units are processed
                "transition_plan": f"{base_url}/transition_plan.json"  # At organization level, not plant level
            }
        else:
            print(f"[Session {session_id}] ⚠️ Plant not found in database: {plant_name}")
            return {"error": "Plant not found in database"}

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error generating hierarchical S3 URLs: {e}")
        return {"error": str(e)}

def check_s3_connection(session_id: str = "test") -> bool:
    """
    Test S3 connection and credentials.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        
        # Try to list objects (this will fail if credentials are wrong)
        s3_client.head_bucket(Bucket=S3_BUCKET)
        print(f"[Session {session_id}] ✅ S3 connection successful to bucket: {S3_BUCKET}")
        return True
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ S3 connection failed: {str(e)}")
        return False

# For testing/debugging
if __name__ == "__main__":
    # Test sanitization
    test_names = [
        "Jhajjar Power Plant",
        "NTPC Dadri (Stage-II)",
        "Adani Mundra Power Station",
        "Tata Power Plant - Unit 1&2"
    ]
    
    print("Testing plant name sanitization:")
    for name in test_names:
        sanitized = sanitize_plant_name(name)
        print(f"'{name}' → '{sanitized}'")
    
    # Test S3 connection
    print("\nTesting S3 connection:")
    check_s3_connection()