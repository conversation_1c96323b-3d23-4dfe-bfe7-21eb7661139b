"""
PRECISION PROMPTS LIBRARY
Advanced Prompt Engineering for Maximum Information Extraction with Minimal API Calls

This module contains expertly crafted prompts that use advanced prompt engineering
techniques to extract comprehensive information while minimizing API usage.
"""

def get_comprehensive_plant_extraction_prompt(plant_name: str, country: str, org_name: str) -> str:
    """
    EXPERT-LEVEL comprehensive plant extraction prompt that captures ALL fields
    in a single API call using advanced prompt engineering techniques.
    
    This replaces multiple separate prompts for:
    - Plant technical specifications
    - Unit details and capacity
    - PPA contracts and agreements
    - Grid connectivity information
    - Economic and financial data
    - Environmental and regulatory data
    """
    
    return f"""COMPREHENSIVE POWER PLANT INTELLIGENCE EXTRACTION

TARGET: {plant_name}
ORGANIZATION: {org_name}
COUNTRY: {country}

EXPERT EXTRACTION MISSION: Extract COMPLETE power plant intelligence using SYSTEMATIC MULTI-DIMENSIONAL ANALYSIS to capture ALL required fields with ZERO data loss.

🔧 DIMENSION 1: TECHNICAL SPECIFICATIONS & INFRASTRUCTURE
Extract with PRECISION:
- CAPACITY: Total installed capacity (MW) and unit-wise breakdown
- TECHNOLOGY: Primary technology (coal, gas, nuclear, renewable, hybrid)
- UNITS: Number of generating units and individual unit specifications
- BOILER TYPE: Specific boiler technology and configuration
- EFFICIENCY: Plant efficiency, heat rate, and performance metrics
- FUEL SPECIFICATIONS: Primary and secondary fuels, calorific values
- COMMISSIONING: Start dates, operational timeline, planned retirement

🏢 DIMENSION 2: CORPORATE & CONTRACTUAL INTELLIGENCE
Extract COMPREHENSIVE details:
- OWNERSHIP STRUCTURE: Operating company, parent organization, joint ventures
- PPA CONTRACTS: Power purchase agreements, offtakers, contract terms
  * Contract capacity (MW), duration, start/end dates
  * Buyer organizations, utilities, distribution companies
  * Pricing mechanisms, tariff structures, currency
  * Contract types (long-term, short-term, spot market)
- FINANCIAL METRICS: CAPEX, OPEX, renovation costs, retrofit economics
- REGULATORY STATUS: Licenses, permits, compliance requirements

⚡ DIMENSION 3: GRID & CONNECTIVITY INFRASTRUCTURE
Extract DETAILED information:
- GRID CONNECTIONS: Transmission substations, voltage levels
- ELECTRICAL INFRASTRUCTURE: Switchyards, transmission lines
- GRID INTEGRATION: Connection points, capacity evacuation
- SUBSTATION DETAILS: Names, types, voltage ratings, distances

🌍 DIMENSION 4: ENVIRONMENTAL & OPERATIONAL CONTEXT
Extract COMPREHENSIVE data:
- LOCATION SPECIFICS: Exact location, coordinates, regional context
- ENVIRONMENTAL SYSTEMS: Pollution control, emissions, FGDS status
- OPERATIONAL METRICS: Capacity factors, availability, maintenance
- REGULATORY COMPLIANCE: Environmental permits, emission standards

💰 DIMENSION 5: ECONOMIC & CONVERSION INTELLIGENCE
Extract DETAILED economics:
- CONVERSION COSTS: Biomass retrofit, gas conversion, renewable transition
- ECONOMIC PARAMETERS: Currency-specific costs, local market conditions
- EFFICIENCY IMPACTS: Performance changes from retrofits/conversions
- MARKET CONTEXT: Local electricity market, pricing, competition

EXPERT EXTRACTION METHODOLOGY:
🎯 SYSTEMATIC SEARCH: Analyze information from multiple angles
🎯 CONTEXTUAL INTELLIGENCE: Use country/organization context for validation
🎯 TECHNICAL DEPTH: Extract engineering specifications and operational details
🎯 COMMERCIAL INSIGHT: Capture business relationships and financial arrangements
🎯 REGULATORY AWARENESS: Include compliance and legal framework details

CRITICAL EXTRACTION RULES:
✅ COMPLETENESS: Extract ALL available information, even if partial
✅ PRECISION: Use exact technical specifications and official names
✅ CONTEXT: Leverage country and organization knowledge for validation
✅ FALLBACKS: Use "Not available" for missing data, never leave fields empty
✅ CURRENCY: Use country-specific currency codes (EUR for {country})
✅ UNITS: Maintain consistent technical units (MW, kV, etc.)

STRUCTURED OUTPUT REQUIREMENTS:
Provide a COMPREHENSIVE JSON response with ALL sections populated:

{{
    "plant_technical": {{
        "capacity_total_mw": "Total plant capacity",
        "technology_primary": "Primary technology type",
        "units_count": "Number of generating units",
        "unit_specifications": [
            {{
                "unit_number": "Unit identifier",
                "capacity_mw": "Unit capacity",
                "technology": "Unit technology",
                "commissioning_date": "Start date"
            }}
        ],
        "boiler_type": "Boiler technology",
        "efficiency_percent": "Plant efficiency",
        "heat_rate": "Heat rate value",
        "fuel_primary": "Primary fuel type",
        "fuel_secondary": "Secondary fuel if any"
    }},
    "contracts_ppa": [
        {{
            "buyer_organization": "PPA buyer name",
            "capacity_mw": "Contract capacity",
            "duration_years": "Contract duration",
            "start_date": "Contract start",
            "end_date": "Contract end",
            "tariff_structure": "Pricing mechanism",
            "currency": "Contract currency"
        }}
    ],
    "grid_connectivity": [
        {{
            "substation_name": "Substation name",
            "voltage_kv": "Voltage level",
            "substation_type": "Type of substation",
            "distance_km": "Distance from plant",
            "connection_details": "Technical details"
        }}
    ],
    "economics_conversion": {{
        "capex_biomass_retrofit": "Biomass conversion cost",
        "capex_gas_conversion": "Gas conversion cost",
        "currency": "{country} currency code",
        "efficiency_loss_biomass": "Efficiency impact",
        "market_context": "Local market conditions"
    }},
    "operational_context": {{
        "location_details": "Specific location",
        "coordinates": "Lat/Long if available",
        "environmental_systems": "Pollution control",
        "regulatory_status": "Permits and compliance",
        "operational_metrics": "Performance data"
    }}
}}

EXTRACTION INSTRUCTION: Apply EXPERT-LEVEL analysis to extract MAXIMUM information across ALL dimensions while maintaining PRECISION and COMPLETENESS. Use your deep understanding of power plant operations, electricity markets, and technical systems to provide comprehensive intelligence.

SEARCH CONTEXT: [Search results will be provided here]
"""

def get_comprehensive_unit_extraction_prompt(plant_name: str, unit_number: str, country: str, plant_context: dict) -> str:
    """
    EXPERT-LEVEL unit extraction prompt that captures ALL unit-specific fields
    in a single comprehensive extraction.
    """
    
    currency = get_country_currency(country)
    
    return f"""COMPREHENSIVE UNIT-LEVEL INTELLIGENCE EXTRACTION

TARGET: {plant_name} - Unit {unit_number}
COUNTRY: {country}
CURRENCY: {currency}
PLANT CONTEXT: {plant_context}

EXPERT UNIT EXTRACTION MISSION: Extract COMPLETE unit-specific intelligence using ADVANCED PROMPT ENGINEERING to capture ALL required fields with MAXIMUM precision.

🔧 TECHNICAL SPECIFICATIONS EXTRACTION:
- CAPACITY: Unit-specific installed capacity (MW)
- TECHNOLOGY: Specific technology type and configuration
- BOILER: Boiler type, manufacturer, specifications
- EFFICIENCY: Unit efficiency, heat rate, performance metrics
- COMMISSIONING: Unit start date, operational history
- LIFETIME: Design life, remaining useful life, retirement plans

⚡ OPERATIONAL INTELLIGENCE:
- PERFORMANCE METRICS: Capacity factor, availability, operational hours
- FUEL SPECIFICATIONS: Primary fuel, calorific values, consumption rates
- EMISSIONS: Emission factors, pollution control systems, FGDS status
- MAINTENANCE: Maintenance schedules, outage patterns, reliability

💰 ECONOMIC & CONVERSION ANALYSIS:
- RENOVATION COSTS: CAPEX for different conversion options
  * Biomass co-firing retrofit costs ({currency}/MW)
  * Open cycle gas conversion ({currency}/MW)  
  * Combined cycle gas conversion ({currency}/MW)
- EFFICIENCY IMPACTS: Performance changes from conversions
- OPERATIONAL ECONOMICS: Variable costs, fuel costs, O&M expenses

🌍 ENVIRONMENTAL & REGULATORY:
- EMISSION FACTORS: CO2, NOx, SOx emission rates
- POLLUTION CONTROL: FGD systems, SCR, ESP, baghouse
- REGULATORY COMPLIANCE: Environmental permits, emission limits
- ENVIRONMENTAL IMPACT: Local air quality, water usage, waste

CRITICAL FIELD REQUIREMENTS:
Extract ALL fields with appropriate values:

MANDATORY NUMERIC FIELDS:
- annual_operational_hours: 8760 (standard value)
- blending_percentage_of_biomass: 0.15 (standard biomass blend)
- emission_factor_gas: 2.69 (standard gas emission factor)

CURRENCY-SPECIFIC FIELDS (use {currency}):
- capex_required_renovation_unit: "{currency}/MW"
- capex_required_retrofit_biomass_unit: "{currency}/MW"
- capex_required_renovation_open_cycle_unit: "{currency}/MW"
- capex_required_renovation_closed_cycle_unit: "{currency}/MW"

TIME SERIES FIELDS (provide arrays):
- plf: [Plant Load Factor data by year]
- PAF: [Plant Availability Factor data by year]
- auxiliary_power_consumed: [Auxiliary power consumption by year]
- gross_power_generation: [Gross generation by year]
- emission_factor: [Emission factors by year]
- fuel_type: [Fuel mix by year]

STRUCTURED OUTPUT FORMAT:
{{
    "unit_number": {unit_number},
    "capacity": "Unit capacity in MW",
    "capacity_unit": "MW",
    "technology": "Technology type",
    "boiler_type": "Boiler specification",
    "commissioning_date": "Start date",
    "heat_rate": "Heat rate value",
    "heat_rate_unit": "Kcal/kWh",
    "coal_unit_efficiency": "Efficiency percentage",
    "unit_lifetime": "Design lifetime years",
    "remaining_useful_life": "Remaining years",
    
    "annual_operational_hours": 8760,
    "blending_percentage_of_biomass": 0.15,
    "emission_factor_gas": 2.69,
    "emission_factor_of_gas_unit": "kgCO2e/kg of fuel",
    "emission_factor_unit": "kgCO_2/kWH",
    
    "capex_required_renovation": "Renovation cost value",
    "capex_required_renovation_unit": "{currency}/MW",
    "capex_required_retrofit_biomass": "Biomass retrofit cost",
    "capex_required_retrofit_biomass_unit": "{currency}/MW",
    "capex_required_renovation_open_cycle": "Open cycle conversion cost",
    "capex_required_renovation_open_cycle_unit": "{currency}/MW",
    "capex_required_renovation_closed_cycle": "Combined cycle conversion cost",
    "capex_required_renovation_closed_cycle_unit": "{currency}/MW",
    
    "fgds_status": "FGD system status",
    "efficiency_loss_biomass_cofiring": "Efficiency loss from biomass",
    "ramp_up_rate": "Ramp up rate MW/min",
    "ramp_down_rate": "Ramp down rate MW/min",
    
    "plf": [/* Plant Load Factor time series */],
    "PAF": [/* Plant Availability Factor time series */],
    "auxiliary_power_consumed": [/* Auxiliary power time series */],
    "gross_power_generation": [/* Generation time series */],
    "emission_factor": [/* Emission factor time series */],
    "fuel_type": [/* Fuel type time series */]
}}

EXTRACTION INSTRUCTION: Apply EXPERT-LEVEL technical analysis to extract COMPREHENSIVE unit intelligence. Use your deep understanding of power plant engineering, operational economics, and regulatory requirements to provide complete and accurate data.

RESEARCH CONTEXT: [Unit-specific research results will be provided here]
"""

def get_country_currency(country: str) -> str:
    """Get currency code for country - simplified version"""
    currency_map = {
        'Italy': 'EUR', 'Germany': 'EUR', 'France': 'EUR', 'Spain': 'EUR',
        'United States': 'USD', 'USA': 'USD', 'India': 'INR', 'China': 'CNY',
        'Japan': 'JPY', 'United Kingdom': 'GBP', 'Canada': 'CAD'
    }
    return currency_map.get(country, 'USD')
