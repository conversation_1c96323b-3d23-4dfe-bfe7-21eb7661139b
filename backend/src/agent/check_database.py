"""
Database Contents Checker

This script shows what's currently in the plant registry database.
"""

from database_manager import get_database_manager

def main():
    """Check and display database contents"""
    
    print("📊 PLANT REGISTRY DATABASE CONTENTS")
    print("=" * 60)
    
    db_manager = get_database_manager()
    
    # Get all plants
    session = db_manager.get_session()
    try:
        from database_manager import PowerPlantRegistry
        all_plants = session.query(PowerPlantRegistry).all()
        
        if not all_plants:
            print("ℹ️ Database is empty")
            return
        
        print(f"📈 Total plants in database: {len(all_plants)}")
        print()
        
        # Group by organization
        orgs = {}
        for plant in all_plants:
            if plant.org_name not in orgs:
                orgs[plant.org_name] = []
            orgs[plant.org_name].append(plant)
        
        print(f"🏢 Organizations: {len(orgs)}")
        print()
        
        # Display by organization
        for org_name, plants in orgs.items():
            print(f"🏢 {org_name}")
            print(f"   Country: {plants[0].country}")
            print(f"   UID: {plants[0].org_id}")
            print(f"   Plants ({len(plants)}):")
            
            for plant in plants:
                print(f"     • {plant.plant_name} ({plant.plant_status.value})")
                print(f"       Plant UID: {plant.plant_id}")  # NEW: Show plant UID
                print(f"       Created: {plant.created_at}")
                if plant.discovered_from_plant:
                    print(f"       Discovered from: {plant.discovered_from_plant}")
            print()
        
        # Show unique Organization UIDs
        unique_org_ids = set(plant.org_id for plant in all_plants)
        print(f"🔑 Unique Organization UIDs: {len(unique_org_ids)}")
        for uid in unique_org_ids:
            org_plants = [p for p in all_plants if p.org_id == uid]
            org_name = org_plants[0].org_name
            print(f"   {uid} → {org_name} ({len(org_plants)} plants)")

        print()

        # Show unique Plant UIDs
        unique_plant_ids = set(plant.plant_id for plant in all_plants if plant.plant_id)
        print(f"🏭 Unique Plant UIDs: {len(unique_plant_ids)}")
        for plant in all_plants:
            if plant.plant_id:
                print(f"   {plant.plant_id} → {plant.plant_name} ({plant.org_name})")

    finally:
        session.close()

if __name__ == "__main__":
    main()