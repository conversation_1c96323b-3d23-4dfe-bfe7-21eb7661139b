from typing import List, Optional, Any, Dict, Union
from pydantic import BaseModel, Field, field_validator


class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


# Plant-level models
class PPARespondent(BaseModel):
    name: str = Field(
        description="The counterparty's name (utility, trader, corporate buyer, etc.)"
    )
    capacity: Union[int, None] = Field(
        description="The capacity volume contracted by this respondent (integer MW or null)"
    )
    currency: str = Field(
        description="The currency in which the price is denominated (e.g., 'USD', 'INR')"
    )
    price: str = Field(
        description="The contracted price per unit of energy or capacity"
    )
    price_unit: str = Field(
        description="The basis for the price (e.g., '$/MWh', 'INR/kW-year')"
    )


class PPADetail(BaseModel):
    capacity: Union[int, None] = Field(
        description="The capacity covered by this PPA (integer MW or null)"
    )
    capacity_unit: str = Field(
        description="The unit of that capacity (e.g., 'MW', 'kW')"
    )
    start_date: str = Field(
        description="The PPA's commencement date (ISO format, YYYY-MM-DD)"
    )
    end_date: str = Field(
        description="The PPA's termination date (ISO format, YYYY-MM-DD). Typically 25 years from the start date."
    )
    tenure: str = Field(
        description="The numeric duration of the PPA (e.g., 20)"
    )
    tenure_type: str = Field(
        description="The unit for the tenure (e.g., 'Years', 'Months')"
    )
    respondents: List[PPARespondent] = Field(
        description="List of entities that have contracted power from this PPA"
    )


class GridConnectivityProject(BaseModel):
    distance: str = Field(
        description="The distance (e.g., in km) from the substation to that project"
    )


class GridConnectivityDetail(BaseModel):
    substation_name: str = Field(
        description="The official name of the substation"
    )
    substation_type: str = Field(
        description="The classification and voltage level of the substation, including any regional or directional qualifier"
    )
    capacity: str = Field(
        description="The rated capacity of the connection at this substation (e.g., in MW)"
    )
    latitude: Union[float, None] = Field(
        description="The geographic latitude of the substation (decimal degrees as float)"
    )
    longitude: Union[float, None] = Field(
        description="The geographic longitude of the substation (decimal degrees as float)"
    )
    projects: List[GridConnectivityProject] = Field(
        description="List of projects connected to this substation"
    )


class GridConnectivityMap(BaseModel):
    details: List[GridConnectivityDetail] = Field(
        description="Details of grid connections for this power plant"
    )


class PlantLevelInfo(BaseModel):
    name: str = Field(
        description="The official name of the power plant"
    )
    plant_id: int = Field(
        description="A unique identifier assigned to this plant in your system (integer)"
    )
    plant_type: str = Field(
        description="The technology or fuel type of the plant site"
    )
    lat: str = Field(
        description="The plant's own latitude coordinate"
    )
    long: str = Field(
        description="The plant's own longitude coordinate"
    )
    plant_address: str = Field(
        description="District or city, State, Country"
    )
    units_id: List[str] = Field(
        description="List of units which are operational at this plant"
    )
    ppa_details: List[PPADetail] = Field(
        description="Details of Power Purchase Agreements for this plant"
    )
    grid_connectivity_maps: List[GridConnectivityMap] = Field(
        description="Information about grid connections for this plant"
    )
    is_ppa: str = Field(
        default="yes",
        description="Indicates whether the plant has Power Purchase Agreements"
    )
    is_retrofitting: str = Field(
        default="yes", 
        description="Indicates whether the plant is undergoing or planned for retrofitting"
    )
    plant_transition_life: int = Field(
        default=5,
        description="The transition life period of the plant in years"
    )


# Unit-level models for detailed unit processing

class YearlyData(BaseModel):
    value: Union[int, float] = Field(description="The numeric data value for the specific year")
    year: int = Field(description="Year for which the data is reported (integer)")

class FuelTypeData(BaseModel):
    fuel: str = Field(default="", description="Fuel source for the plant/unit (Coal, Natural Gas, Biomass, Oil, etc.)")
    type: str = Field(default="", description="Subcategory of the fuel source (e.g., bituminous, sub-bituminous, lignite for coal)")
    years_percentage: Dict[str, Union[str, float]] = Field(
        description="Dictionary mapping year to percentage of fuel used in that year",
        default_factory=dict
    )
    
    @field_validator('years_percentage', mode='before')
    @classmethod
    def validate_years_percentage(cls, v):
        """Handle cases where 'None' string is passed instead of dict"""
        if v is None or v == 'None' or v == '' or v == 'null':
            return {}
        if isinstance(v, str):
            # Try to parse as JSON if it's a string
            try:
                import json
                return json.loads(v)
            except:
                return {}
        if isinstance(v, dict):
            return v
        return {}



class UnitLevelInfo(BaseModel):
    """Complete unit-level information schema based on unit_level.json specification"""
    
    # Basic Unit Identification
    sk: str = Field(default="", description="Unique identifier: unit#plant_type#unit_id#plant#plant_id")
    unit_number: int = Field(default=1, description="Unit number as integer")
    plant_id: int = Field(default=1, description="Sequential plant identifier starting from 1")
    
    # Technical Specifications
    capacity: Union[float, None] = Field(default=None, description="Unit-wise installed capacity of the plant in megawatts (MW)")
    capacity_unit: str = Field(default="MW", description="Unit for installed capacity, typically 'MW'")
    technology: str = Field(default="", description="Coal - Ultra Super Critical, Super Critical, Critical, Sub-critical. Natural Gas - Single/Open Cycle, Combined/Closed Cycle. Biomass - Fluidized Bed Reactor, Direct Combustion, Boiler Conversion")
    boiler_type: str = Field(default="", description="Type of boiler used in the power plant/unit")
    commencement_date: str = Field(default="", description="The date of commercial operation of a specific unit. Format: yyyy-mm-ddThh:mm:ss.msZ")
    remaining_useful_life: Union[float, None] = Field(default=None, description="Remaining operational years for the unit (calculated from current date to end-of-life)")
    unit_lifetime: Union[float, None] = Field(default=None, description="Total operational lifetime of the unit in years")
    
    # Performance Metrics (Time-series data)
    plf: List[YearlyData] = Field(
        description="Plant Load Factor (PLF) data by year, represented in %",
        default_factory=list
    )
    PAF: List[YearlyData] = Field(
        description="Plant Availability Factor (PAF) data by year",
        default_factory=list
    )
    auxiliary_power_consumed: List[YearlyData] = Field(
        description="Auxiliary power consumption data by year as percentage of gross generation",
        default_factory=list
    )
    gross_power_generation: List[YearlyData] = Field(
        description="Total energy generated by the unit/plant by year",
        default_factory=list
    )
    emission_factor: List[YearlyData] = Field(
        description="CO2 emissions per kWh generation by year (kg CO2e/kWh)",
        default_factory=list
    )
    
    # Efficiency Metrics
    coal_unit_efficiency: Union[float, None] = Field(default=None, description="Unit specific efficiency of the coal power plant unit")

    heat_rate: Union[float, None] = Field(default=None, description="Station Heat Rate - fuel energy required to produce one kWh of electricity")
    heat_rate_unit: str = Field(default="kJ/kWh", description="Unit for heat rate (usually kJ/kWh or kcal/kWh)")
    
    # Fuel Information
    fuel_type: List[FuelTypeData] = Field(
        description="Detailed fuel information including type and yearly usage percentages",
        default_factory=list
    )

    
    # Country-specific Parameters
    gcv_coal: Union[float, None] = Field(default=None, description="Gross calorific value of coal in the country (kCal/kg)")
    gcv_coal_unit: str = Field(default="kCal/kg", description="Unit for GCV of coal (e.g., kCal/kg)")
    gcv_natural_gas: Union[float, None] = Field(default=10000, description="Gross calorific value of natural gas in the country")
    gcv_natural_gas_unit: str = Field(default="kcal/scm", description="Unit for GCV of natural gas")
    gcv_biomass: Union[float, None] = Field(default=None, description="Gross calorific value of biomass in the country (kCal/kg)")
    gcv_biomass_unit: str = Field(default="kCal/kg", description="Unit for GCV of biomass (e.g., kCal/kg)")
    
    # Technology-specific Efficiency (Country-specific)
    open_cycle_gas_turbine_efficency: Union[float, None] = Field(default=None, description="OCGT efficiency for the specific country")
    closed_cylce_gas_turbine_efficency: Union[float, None] = Field(default=None, description="CCGT efficiency for the specific country")
    combined_cycle_heat_rate: Union[float, None] = Field(default=None, description="Heat rate for CCGT plants in the country")
    open_cycle_heat_rate: Union[float, None] = Field(default=None, description="Heat rate for OCGT plants in the country")
    
    # Conversion Economics
    capex_required_retrofit_biomass: Union[float, None] = Field(default=None, description="CAPEX required to retrofit for biomass cofiring")
    capex_required_retrofit_biomass_unit: str = Field(default="Million USD", description="Unit for retrofit CAPEX (Million in local currency)")
    capex_required_renovation_open_cycle: Union[float, None] = Field(default=None, description="CAPEX for retrofitting to open cycle natural gas")
    capex_required_renovation_open_cycle_unit: str = Field(default="USD/MW", description="Unit for open cycle renovation CAPEX (e.g., USD/MW)")
    capex_required_renovation_closed_cycle: Union[float, None] = Field(default=None, description="CAPEX for retrofitting to closed cycle natural gas")
    capex_required_renovation_closed_cycle_unit: str = Field(default="USD/MW", description="Unit for closed cycle renovation CAPEX (e.g., USD/MW)")
    efficiency_loss_biomass_cofiring: Union[float, None] = Field(default=None, description="Efficiency reduction (%) from biomass cofiring retrofit")


class PowerPlantInfo(BaseModel):
    # Organization-level information
    cfpp_type: str = Field(
        description="Classification of the power plant's ownership structure: 'Public' (government-owned), 'Private' (owned by private companies or investors), or 'Joint-Venture' (shared ownership between public and private entities). This indicates the governance and funding structure of the plant."
    )
    country_name: str = Field(
        description="The full official name of the country where the power plant is physically located. This should be the internationally recognized country name, not abbreviations or colloquial names."
    )
    currency_in: str = Field(
        description="The three-letter ISO 4217 currency code (e.g., USD, EUR, JPY) used in the country where the power plant operates. This is relevant for financial reporting and economic analysis of the plant's operations."
    )
    financial_year: str = Field(
        description="The standard fiscal/financial year reporting period used in the country where the plant operates, expressed in MM-MM format (e.g., '04-03' for April to March, '01-12' for January to December). This indicates when the organization's annual financial reporting cycle begins and ends."
    )
    organization_name: str = Field(
        description="The complete legal name of the company, utility, or entity that owns or operates the power plant. This should be the official registered name, not abbreviations or trade names."
    )
    plants_count: int = Field(
        description="The exact number of distinct POWER PLANT SITES/FACILITIES owned or operated by the organization. This must be a precise number, not a vague term like 'Multiple'. CRITICAL: This counts separate physical plant locations/sites, NOT individual generating units within those sites. Example: If 'Empresa Generadora de Electricidad Itabo, S.A.' owns only 'Itabo Power Station' (which has multiple units), plants_count = 1."
    )
    plant_types: List[str] = Field(
        description="A comprehensive list of all power generation technologies employed by the power plant across its facilities (e.g., 'Coal', 'Natural Gas', 'Hydroelectric', 'Nuclear', 'Solar PV', 'Wind', etc.). This indicates the powerplant's energy generation portfolio."
    )
    ppa_flag: str = Field(
        description="Indicates whether Power Purchase Agreements (PPAs) are structured at the 'Plant-level' (applying to the entire facility's output) or 'Unit-level' (separate agreements for individual generating units within the plant). This affects how electricity sales are contracted and managed."
    )
    province: str = Field(
        description="The specific sub-national administrative region (state, province, prefecture, district, etc.) where the power plant is physically located. This provides more precise geographical context than just the country name."
    )
    
    # Plant-level information
    name: str = Field(
        description="The official name of the power plant"
    )
    plant_name: str = Field(
        description="The official name of the power plant (duplicate of name field)"
    )
    state: str = Field(
        description="The state where the power plant is located"
    )
    plant_id: int = Field(
        description="A unique sequential identifier assigned to this plant starting from 1"
    )
    plant_type: str = Field(
        description="The technology or fuel type of the plant site (lowercase: coal, natural gas, biomass, etc.)"
    )
    lat: Union[float, None] = Field(
        description="The plant's own latitude coordinate (decimal degrees as float)"
    )
    long: Union[float, None] = Field(
        description="The plant's own longitude coordinate (decimal degrees as float)"
    )
    plant_address: str = Field(
        description="District or city, State, Country"
    )
    units_id: List[int] = Field(
        description="List of unit numbers (integers) which are operational at this plant"
    )
    
    # PPA details with full structure matching the schema
    ppa_details: List[PPADetail] = Field(
        default_factory=list,
        description="Details of Power Purchase Agreements for this plant"
    )
    
    # Grid connectivity with full structure matching the schema
    grid_connectivity_maps: List[GridConnectivityMap] = Field(
        default_factory=list,
        description="Information about grid connections for this plant"
    )
