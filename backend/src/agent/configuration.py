import os
from pydantic import BaseModel, <PERSON>
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig


class Configuration(BaseModel):
    """The configuration for the power plant information extraction agent."""

    query_generator_model: str = Field(
        default="gemini-2.0-flash",
        json_schema_extra={
            "description": "The name of the language model to use for the agent's query generation."
        },
    )

    reflection_model: str = Field(
        default="gemini-2.0-flash",
        json_schema_extra={
            "description": "The name of the language model to use for the agent's reflection."
        },
    )

    reasoning_model: str = Field(
        default="gemini-2.5-pro-preview-05-06",
        json_schema_extra={
            "description": "The name of the language model to use for the agent's reasoning and structured output generation."
        },
    )

    web_searcher_model: str = Field(
        default="gemini-2.0-flash",
        json_schema_extra={
            "description": "The name of the language model to use for the agent's web search operations."
        },
    )

    number_of_initial_queries: int = Field(
        default=5,
        json_schema_extra={"description": "The number of initial search queries to generate for power plant information."},
    )

    max_research_loops: int = Field(
        default=3,
        json_schema_extra={"description": "The maximum number of research loops to perform to gather complete power plant information."},
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # Get raw values from environment or config
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # Filter out None values
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
