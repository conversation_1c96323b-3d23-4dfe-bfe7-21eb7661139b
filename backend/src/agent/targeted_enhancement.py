"""
Targeted Enhancement Module - Hybrid Approach
Performs additional targeted searches for missing foundation data
when the main extraction doesn't find critical fields needed for calculations.
"""

import logging
from typing import Dict, List, Optional, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage
import os

logger = logging.getLogger(__name__)

class TargetedEnhancementEngine:
    """
    Engine that performs targeted searches for missing foundation data
    """
    
    def __init__(self):
        self.max_enhancement_searches = 3  # Budget limit per unit
    
    def detect_critical_gaps(self, extracted_data: Dict, session_id: str = "unknown") -> List[str]:
        """
        Identify what foundation data is missing that would prevent calculations
        
        Args:
            extracted_data: Data from 5-stage extraction
            session_id: Session identifier for logging
            
        Returns:
            List of critical missing fields
        """
        critical_gaps = []
        
        # Check foundation data for calculations
        if not self._has_valid_capacity(extracted_data):
            critical_gaps.append("capacity")
        
        if not self._has_valid_technology(extracted_data):
            critical_gaps.append("technology")
            
        if not self._has_valid_generation_data(extracted_data):
            critical_gaps.append("generation_data")
            
        if not self._has_valid_fuel_type(extracted_data):
            critical_gaps.append("fuel_type")
        
        if critical_gaps:
            print(f"[Session {session_id}] 🔍 CRITICAL GAPS DETECTED: {', '.join(critical_gaps)}")
        else:
            print(f"[Session {session_id}] ✅ All foundation data available")
            
        return critical_gaps
    
    def enhance_missing_foundation_data(self, 
                                      critical_gaps: List[str], 
                                      plant_context: Dict, 
                                      original_research_data: str,
                                      session_id: str = "unknown") -> Dict:
        """
        Perform targeted searches for missing foundation data
        
        Args:
            critical_gaps: List of missing critical fields
            plant_context: Plant context information
            original_research_data: Original research data for reference
            session_id: Session identifier
            
        Returns:
            Dictionary with enhanced data for missing fields
        """
        enhanced_data = {}
        searches_used = 0
        
        plant_name = plant_context.get("plant_name", "Unknown")
        unit_num = plant_context.get("unit_number", "1")
        
        print(f"[Session {session_id}] 🎯 TARGETED ENHANCEMENT: Processing {len(critical_gaps)} gaps")
        
        # Priority order for enhancement (most critical first)
        enhancement_priority = ["capacity", "technology", "generation_data", "fuel_type"]
        
        for gap_type in enhancement_priority:
            if gap_type in critical_gaps and searches_used < self.max_enhancement_searches:
                print(f"[Session {session_id}] 🔍 Enhancing: {gap_type}")
                
                try:
                    if gap_type == "capacity":
                        result = self._search_for_capacity(plant_name, unit_num, original_research_data)
                    elif gap_type == "technology":
                        result = self._search_for_technology(plant_name, unit_num, original_research_data)
                    elif gap_type == "generation_data":
                        result = self._search_for_generation_data(plant_name, unit_num, original_research_data)
                    elif gap_type == "fuel_type":
                        result = self._search_for_fuel_type(plant_name, unit_num, original_research_data)
                    else:
                        result = {}
                    
                    if result:
                        enhanced_data.update(result)
                        print(f"[Session {session_id}] ✅ Enhanced {gap_type}: {result}")
                        searches_used += 1
                    else:
                        print(f"[Session {session_id}] ❌ No enhancement found for {gap_type}")
                        
                except Exception as e:
                    print(f"[Session {session_id}] ❌ Enhancement error for {gap_type}: {str(e)}")
                    logger.error(f"Enhancement error for {gap_type}: {str(e)}")
        
        print(f"[Session {session_id}] 📊 ENHANCEMENT COMPLETE: Used {searches_used}/{self.max_enhancement_searches} searches")
        return enhanced_data
    
    def _has_valid_capacity(self, data: Dict) -> bool:
        """Check if capacity data is available and valid"""
        capacity = data.get("capacity", "")
        if not capacity or capacity in ["", "Not available", "N/A"]:
            return False
        
        # Try to extract numeric value
        try:
            numeric_value = float(str(capacity).replace("MW", "").replace(",", "").strip())
            return numeric_value > 0
        except:
            return False
    
    def _has_valid_technology(self, data: Dict) -> bool:
        """Check if technology data is available and valid"""
        technology = data.get("technology", "")
        if not technology or technology in ["", "Not available", "N/A"]:
            return False
        
        # Check if it's a meaningful technology type
        valid_techs = ["critical", "sub", "super", "ultra", "combined", "cycle", "gas", "coal"]
        tech_lower = technology.lower()
        return any(valid_tech in tech_lower for valid_tech in valid_techs)
    
    def _has_valid_generation_data(self, data: Dict) -> bool:
        """Check if generation data is available and valid"""
        generation = data.get("gross_power_generation", [])
        if not generation or len(generation) == 0:
            return False
        
        # Check if we have at least one valid generation record
        for record in generation:
            if isinstance(record, dict) and "value" in record:
                try:
                    value = float(str(record["value"]).replace(",", "").strip())
                    if value > 0:
                        return True
                except:
                    continue
        return False
    
    def _has_valid_fuel_type(self, data: Dict) -> bool:
        """Check if fuel type data is available and valid (coal only, exclude biomass)"""
        fuel_type = data.get("fuel_type", [])

        # Check fuel_type array for coal entries only
        if fuel_type and len(fuel_type) > 0:
            for fuel in fuel_type:
                if isinstance(fuel, dict) and fuel.get("fuel") and fuel.get("type"):
                    fuel_name = fuel.get("fuel", "").lower()
                    fuel_type_name = fuel.get("type", "").lower()

                    # Only consider coal fuel types, exclude biomass
                    is_coal = (
                        "coal" in fuel_name or
                        any(coal_type in fuel_type_name for coal_type in [
                            "bituminous", "sub-bituminous", "lignite", "anthracite"
                        ])
                    )

                    is_biomass = (
                        "biomass" in fuel_name or
                        "wood" in fuel_type_name or "pellet" in fuel_type_name or
                        "pks" in fuel_type_name or "palm" in fuel_type_name or
                        "kernel" in fuel_type_name or "husk" in fuel_type_name or
                        "shell" in fuel_type_name
                    )

                    # Return True only if we have valid coal fuel type (not biomass)
                    if is_coal and not is_biomass:
                        return True

        return False
    
    def _search_for_capacity(self, plant_name: str, unit_num: str, research_data: str) -> Dict:
        """Targeted search for unit capacity information"""
        
        prompt = f"""From the research data below, extract ONLY the capacity information for Unit {unit_num}.

SPECIFIC EXTRACTION TASK:
- Look for "Unit {unit_num}" + capacity/MW information
- Look for total plant capacity and divide by number of units if unit-specific not found
- Extract any capacity specifications, technical documents, or power ratings
- Focus on installed capacity, rated capacity, or maximum output

Plant: {plant_name}
Unit: {unit_num}

Research Data:
{research_data}

Respond with JSON format ONLY:
{{
  "capacity": "extracted_capacity_value_in_MW",
  "capacity_unit": "MW",
  "capacity_source": "description_of_where_found"
}}

If no capacity information found, respond with:
{{
  "capacity": "Not found",
  "capacity_unit": "MW",
  "capacity_source": "No capacity data in research"
}}"""

        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                temperature=0,
                max_retries=2,
                api_key=os.getenv("GEMINI_API_KEY"),
            )
            
            result = llm.invoke(prompt)
            content = result.content.strip()
            
            # Parse JSON response
            import json
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Check if we got valid capacity
                if parsed_data.get("capacity") and parsed_data["capacity"] != "Not found":
                    return parsed_data
            
            return {}
            
        except Exception as e:
            logger.error(f"Capacity search error: {str(e)}")
            return {}
    
    def _search_for_technology(self, plant_name: str, unit_num: str, research_data: str) -> Dict:
        """Targeted search for technology type information"""
        
        prompt = f"""From the research data below, extract ONLY the technology type for Unit {unit_num}.

SPECIFIC EXTRACTION TASK:
- Look for boiler technology, turbine technology, power generation technology
- Look for terms like: Super Critical, Sub Critical, Ultra Super Critical, Combined Cycle
- Look for pressure ratings, temperature specifications, steam conditions
- Look for technical specifications, engineering documents

Plant: {plant_name}
Unit: {unit_num}

Research Data:
{research_data}

Respond with JSON format ONLY:
{{
  "technology": "extracted_technology_type",
  "technology_source": "description_of_where_found"
}}

Common technology types: "Super Critical", "Sub Critical", "Ultra Super Critical", "Combined Cycle", "Simple Cycle"

If no technology information found, respond with:
{{
  "technology": "Not found",
  "technology_source": "No technology data in research"
}}"""

        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                temperature=0,
                max_retries=2,
                api_key=os.getenv("GEMINI_API_KEY"),
            )
            
            result = llm.invoke(prompt)
            content = result.content.strip()
            
            # Parse JSON response
            import json
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Check if we got valid technology
                if parsed_data.get("technology") and parsed_data["technology"] != "Not found":
                    return parsed_data
            
            return {}
            
        except Exception as e:
            logger.error(f"Technology search error: {str(e)}")
            return {}
    
    def _search_for_generation_data(self, plant_name: str, unit_num: str, research_data: str) -> Dict:
        """Targeted search for generation data"""
        
        prompt = f"""From the research data below, extract ONLY the generation data for Unit {unit_num}.

SPECIFIC EXTRACTION TASK:
- Look for annual generation, electricity generation, power output, energy generated
- Look for annual production, generation capacity, power produced
- Look for MWh, GWh, million units, billion units, TWh
- Look for yearly generation figures, capacity utilization data
- Look for operational statistics, performance reports
- Look for availability hours, operational hours, plant availability
- Look for "No.of hours plant was available", available hours, downtime hours
- Look for annual emissions, CO2 emissions, carbon emissions, total emissions
- Look for emission factors, carbon intensity, CO2/kWh, emission data

Plant: {plant_name}
Unit: {unit_num}

Research Data:
{research_data}

Respond with JSON format ONLY:
{{
  "gross_power_generation": [
    {{"value": "generation_amount_in_MWh", "year": "year"}},
    {{"value": "generation_amount_in_MWh", "year": "year"}}
  ],
  "generation_source": "description_of_where_found"
}}

If no generation information found, respond with:
{{
  "gross_power_generation": [],
  "generation_source": "No generation data in research"
}}"""

        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                temperature=0,
                max_retries=2,
                api_key=os.getenv("GEMINI_API_KEY"),
            )
            
            result = llm.invoke(prompt)
            content = result.content.strip()
            
            # Parse JSON response
            import json
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Check if we got valid generation data
                generation_data = parsed_data.get("gross_power_generation", [])
                if generation_data and len(generation_data) > 0:
                    return parsed_data
            
            return {}
            
        except Exception as e:
            logger.error(f"Generation data search error: {str(e)}")
            return {}
    
    def _search_for_fuel_type(self, plant_name: str, unit_num: str, research_data: str) -> Dict:
        """Targeted search for fuel type information"""
        
        prompt = f"""From the research data below, extract ONLY the fuel type information for Unit {unit_num}.

SPECIFIC EXTRACTION TASK:
- Look for coal type, fuel specifications, coal quality
- Look for terms like: Bituminous, Sub-bituminous, Lignite, Anthracite
- Look for fuel suppliers, coal sources, fuel analysis reports
- Look for environmental permits, fuel consumption data

Plant: {plant_name}
Unit: {unit_num}

Research Data:
{research_data}

Respond with JSON format ONLY:
{{
  "fuel_type": [
    {{"fuel": "Coal", "type": "Bituminous", "years_percentage": {{"2023": "percentage"}}}}
  ],

  "fuel_source": "description_of_where_found"
}}

Common coal types: "Bituminous", "Sub-bituminous", "Lignite", "Anthracite"

If no fuel information found, respond with:
{{
  "fuel_type": [],

  "fuel_source": "No fuel data in research"
}}"""

        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                temperature=0,
                max_retries=2,
                api_key=os.getenv("GEMINI_API_KEY"),
            )
            
            result = llm.invoke(prompt)
            content = result.content.strip()
            
            # Parse JSON response
            import json
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Check if we got valid fuel data
                fuel_data = parsed_data.get("fuel_type", [])

                
                if fuel_data and len(fuel_data) > 0:
                    return parsed_data
            
            return {}
            
        except Exception as e:
            logger.error(f"Fuel type search error: {str(e)}")
            return {}


# Global enhancement engine
ENHANCEMENT_ENGINE = TargetedEnhancementEngine()


def apply_targeted_enhancement(extracted_data: Dict, 
                             plant_context: Dict, 
                             original_research_data: str,
                             session_id: str = "unknown") -> Dict:
    """
    Convenience function to apply targeted enhancement
    
    Args:
        extracted_data: Data from 5-stage extraction
        plant_context: Plant context information
        original_research_data: Original research data
        session_id: Session identifier
        
    Returns:
        Enhanced data with missing foundation fields filled
    """
    
    # Detect critical gaps
    critical_gaps = ENHANCEMENT_ENGINE.detect_critical_gaps(extracted_data, session_id)
    
    if not critical_gaps:
        print(f"[Session {session_id}] ✅ No targeted enhancement needed")
        return extracted_data
    
    # Apply targeted enhancement
    enhanced_fields = ENHANCEMENT_ENGINE.enhance_missing_foundation_data(
        critical_gaps, plant_context, original_research_data, session_id
    )
    
    # Merge enhanced data with original
    final_data = extracted_data.copy()
    final_data.update(enhanced_fields)
    
    return final_data