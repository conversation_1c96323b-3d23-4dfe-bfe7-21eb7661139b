#!/usr/bin/env python3
"""
Standalone test for Australia Excel integration functionality

This test verifies the Excel data extraction without importing the full pipeline.
"""

import pandas as pd
import os
from pathlib import Path
from typing import Dict, List, Optional

class StandaloneAustraliaExcelIntegrator:
    """Standalone version of Australia Excel integrator for testing"""
    
    def __init__(self):
        self.excel_path = "Australia_details.xlsx"
        self.df = None
        self._load_excel_data()
    
    def _load_excel_data(self):
        """Load and preprocess the Excel data"""
        try:
            if not os.path.exists(self.excel_path):
                print(f"❌ Australia Excel file not found: {self.excel_path}")
                return
            
            # Read Excel file
            self.df = pd.read_excel(self.excel_path)
            
            # Remove the header row (row 0 contains 'gross_power_generation', 'Emission_factor')
            self.df = self.df.iloc[1:].reset_index(drop=True)
            
            # Create a mapping of years to column indices
            self.year_columns = {}
            self.emission_columns = {}
            
            for i, col in enumerate(self.df.columns):
                if isinstance(col, int) and 2020 <= col <= 2024:  # Year columns
                    self.year_columns[col] = i
                    # Next column should be emission factor
                    if i + 1 < len(self.df.columns):
                        self.emission_columns[col] = i + 1
            
            print(f"✅ Australia Excel data loaded: {len(self.df)} plants")
            print(f"📊 Year columns mapped: {self.year_columns}")
            print(f"📊 Emission columns mapped: {self.emission_columns}")
            
        except Exception as e:
            print(f"❌ Error loading Australia Excel data: {e}")
            self.df = None
    
    def is_data_available(self) -> bool:
        """Check if Excel data is available"""
        return self.df is not None and not self.df.empty
    
    def find_plant_data(self, plant_name: str, entity_name: Optional[str] = None) -> Optional[Dict]:
        """Find plant data in the Excel file"""
        if not self.is_data_available():
            return None
        
        # Clean plant name for matching
        clean_plant_name = self._clean_plant_name(plant_name)
        
        # Try exact match first
        for idx, row in self.df.iterrows():
            excel_plant_name = str(row['Plant Name']).lower().strip()
            excel_clean_name = self._clean_plant_name(excel_plant_name)
            
            if clean_plant_name == excel_clean_name:
                return self._extract_plant_data(row)
        
        # Try fuzzy matching
        for idx, row in self.df.iterrows():
            excel_plant_name = str(row['Plant Name']).lower().strip()
            excel_clean_name = self._clean_plant_name(excel_plant_name)
            
            if clean_plant_name in excel_clean_name or excel_clean_name in clean_plant_name:
                print(f"🔍 Fuzzy match found: {row['Plant Name']}")
                return self._extract_plant_data(row)
        
        print(f"❌ No match found in Australia Excel for: {plant_name}")
        return None
    
    def _clean_plant_name(self, plant_name: str) -> str:
        """Clean plant name for better matching"""
        clean_name = plant_name.lower().strip()
        
        # Remove common power plant suffixes
        suffixes_to_remove = [
            'power station', 'power plant', 'generating station', 
            'generation station', 'thermal power station', 'coal power station'
        ]
        
        for suffix in suffixes_to_remove:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)].strip()
        
        return clean_name
    
    def _extract_plant_data(self, row) -> Dict:
        """Extract plant data from Excel row"""
        plant_data = {
            "entity_name": str(row['Entity Name']),
            "plant_name": str(row['Plant Name']),
            "data_source": "Australia_details.xlsx",
            "gross_power_generation": {},
            "emission_factor": {},
            "years_available": list(self.year_columns.keys())
        }
        
        # Extract year-wise data
        for year in self.year_columns.keys():
            try:
                # Get gross power generation
                gross_col_idx = self.year_columns[year]
                gross_value = row.iloc[gross_col_idx]
                
                # Clean and convert gross power generation
                if pd.notna(gross_value) and gross_value != 0:
                    plant_data["gross_power_generation"][str(year)] = {
                        "value": float(gross_value),
                        "unit": "MWh",
                        "source": "Australia_details.xlsx"
                    }
                
                # Get emission factor
                if year in self.emission_columns:
                    emission_col_idx = self.emission_columns[year]
                    emission_value = row.iloc[emission_col_idx]
                    
                    # Clean and convert emission factor
                    if pd.notna(emission_value) and emission_value != 0:
                        plant_data["emission_factor"][str(year)] = {
                            "value": float(emission_value),
                            "unit": "tCO2/MWh",
                            "source": "Australia_details.xlsx"
                        }
                        
            except (ValueError, IndexError) as e:
                print(f"⚠️ Error extracting data for year {year}: {e}")
                continue
        
        return plant_data

def test_standalone_integration():
    """Test the standalone integration functionality"""
    print("🧪 Standalone Australia Excel Integration Test")
    print("=" * 50)

    # Initialize integrator
    integrator = StandaloneAustraliaExcelIntegrator()

    if not integrator.is_data_available():
        print("❌ Excel data not available")
        return False

    # Test cases
    test_cases = [
        ("Bayswater Power Station", "AGL ENERGY LIMITED"),
        ("Liddell Power Station", None),
        ("Loy Yang Power Station and Mine", None),
        ("Unknown Plant", None)  # Should not match
    ]

    print(f"\n🔍 Testing plant matching...")

    for plant_name, entity_name in test_cases:
        print(f"\n📋 Testing: {plant_name}")
        result = integrator.find_plant_data(plant_name, entity_name)

        if result:
            print(f"   ✅ Match found: {result['plant_name']}")
            print(f"   🏢 Entity: {result['entity_name']}")
            print(f"   📅 Years available: {result['years_available']}")

            if result['gross_power_generation']:
                print(f"   📈 Gross power generation data for {len(result['gross_power_generation'])} years:")
                for year, data in list(result['gross_power_generation'].items())[:3]:
                    print(f"      {year}: {data['value']:,} {data['unit']}")

            if result['emission_factor']:
                print(f"   🌿 Emission factor data for {len(result['emission_factor'])} years:")
                for year, data in list(result['emission_factor'].items())[:3]:
                    print(f"      {year}: {data['value']} {data['unit']}")
        else:
            print(f"   ❌ No match found")

    print(f"\n✅ Standalone integration test completed successfully!")
    return True


def test_unit_level_integration():
    """Test unit-level integration functionality"""
    print("\n🧪 Unit-Level Australia Excel Integration Test")
    print("=" * 50)

    # Mock unit data structure (similar to what combine_unit_data produces)
    mock_unit_data = {
        "sk": "unit#coal#1#plant#1",
        "unit_number": "1",
        "plant_id": "1",
        "plant_uid": "test-plant-uuid",
        "capacity": "660",
        "capacity_unit": "MW",
        "technology": "Super Critical",
        "boiler_type": "Pulverized Coal",
        "commencement_date": "2012-01-01T00:00:00.000Z",

        # Default time-series data (will be replaced by Excel data)
        "gross_power_generation": [
            {"value": "2500000", "year": "2023"},
            {"value": "2400000", "year": "2022"}
        ],
        "emission_factor": [
            {"value": "0.95", "year": "2023"},
            {"value": "0.97", "year": "2022"}
        ],

        # Other required fields
        "plf": [{"value": "65.0", "year": "2023"}],
        "PAF": [{"value": "85.0", "year": "2023"}],
        "auxiliary_power_consumed": [{"value": "8.5", "year": "2023"}],
        "fuel_type": [{"value": "Coal", "year": "2023"}]
    }

    # Test cases for different plants
    test_cases = [
        {
            "plant_name": "Bayswater Power Station",
            "country": "Australia",
            "description": "Australian coal plant (should integrate)"
        },
        {
            "plant_name": "Some Indian Plant",
            "country": "India",
            "description": "Non-Australian plant (should skip)"
        },
        {
            "plant_name": "Unknown Australian Plant",
            "country": "Australia",
            "description": "Australian plant not in Excel (should skip)"
        }
    ]

    # Import the unit-level integration function
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        from agent.australia_excel_integration import integrate_australia_excel_data_for_unit

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}: {test_case['description']}")
            print(f"   Plant: {test_case['plant_name']}")
            print(f"   Country: {test_case['country']}")

            # Create a copy of mock data for this test
            test_unit_data = mock_unit_data.copy()

            # Apply integration
            result = integrate_australia_excel_data_for_unit(
                unit_info=test_unit_data,
                plant_name=test_case['plant_name'],
                country=test_case['country'],
                session_id=f"test_{i}"
            )

            # Check results
            if "australia_excel_integration" in result:
                integration_meta = result["australia_excel_integration"]
                print(f"   ✅ Integration applied!")
                print(f"      Excel entity: {integration_meta['excel_entity_name']}")
                print(f"      Excel plant: {integration_meta['excel_plant_name']}")
                print(f"      Years available: {integration_meta['years_available']}")

                # Show updated gross power generation
                if "gross_power_generation" in result:
                    print(f"   📈 Updated gross power generation:")
                    for entry in result["gross_power_generation"][:3]:
                        print(f"      {entry['year']}: {entry['value']} MWh")

                # Show updated emission factor
                if "emission_factor" in result:
                    print(f"   🌿 Updated emission factor:")
                    for entry in result["emission_factor"][:3]:
                        print(f"      {entry['year']}: {entry['value']} tCO2/MWh")
            else:
                print(f"   ⏭️ Integration skipped (as expected)")

                # Verify original data is preserved
                if result["gross_power_generation"] == mock_unit_data["gross_power_generation"]:
                    print(f"   ✅ Original data preserved")
                else:
                    print(f"   ⚠️ Original data was modified unexpectedly")

        print(f"\n✅ Unit-level integration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error during unit-level integration test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run both tests
    success1 = test_standalone_integration()
    success2 = test_unit_level_integration()

    if success1 and success2:
        print(f"\n🎉 All tests passed successfully!")
    else:
        print(f"\n❌ Some tests failed")
