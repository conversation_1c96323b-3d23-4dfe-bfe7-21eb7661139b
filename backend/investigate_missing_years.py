#!/usr/bin/env python3
"""
Investigate the missing years (2020, 2021) in the Excel file
"""

import pandas as pd
import numpy as np

def investigate_missing_years():
    """Investigate if 2020 and 2021 data exists in unnamed columns"""
    print("🔍 INVESTIGATING MISSING YEARS (2020, 2021)")
    print("=" * 60)
    
    try:
        # Load the Excel file
        df = pd.read_excel('Australia_details.xlsx')
        print(f"✅ Excel file loaded successfully")
        print()
        
        # Show the header row (row 0) to understand column meanings
        print("📋 HEADER ROW ANALYSIS:")
        header_row = df.iloc[0]
        for i, val in enumerate(header_row):
            col_name = df.columns[i]
            print(f"  Column {i:2d} ('{col_name}'): '{val}'")
        print()
        
        # Look for patterns that might indicate 2020/2021 data
        print("🔍 SEARCHING FOR 2020/2021 PATTERNS:")
        header_values = header_row.astype(str).str.lower()
        
        for i, val in enumerate(header_values):
            if '2020' in val or '2021' in val:
                print(f"  ✅ Found year reference in column {i}: '{header_row.iloc[i]}'")
            elif 'gross' in val or 'power' in val or 'generation' in val:
                print(f"  🔍 Found power-related term in column {i}: '{header_row.iloc[i]}'")
            elif 'emission' in val or 'factor' in val:
                print(f"  🌿 Found emission-related term in column {i}: '{header_row.iloc[i]}'")
        print()
        
        # Remove header row and analyze data
        df_data = df.iloc[1:].reset_index(drop=True)
        
        # Look at Bayswater data in all columns
        print("🏭 BAYSWATER DATA IN ALL COLUMNS:")
        bayswater_mask = df_data['Plant Name'].str.contains('Bayswater', case=False, na=False)
        if bayswater_mask.any():
            row = df_data[bayswater_mask].iloc[0]
            
            for i, val in enumerate(row):
                col_name = df.columns[i]
                header_val = header_row.iloc[i]
                print(f"  Column {i:2d} ('{col_name}' | header: '{header_val}'): {val}")
        print()
        
        # Check if unnamed columns contain numeric data that could be years
        print("📊 UNNAMED COLUMNS NUMERIC DATA ANALYSIS:")
        unnamed_cols = [i for i, col in enumerate(df.columns) if 'Unnamed' in str(col)]
        
        for col_idx in unnamed_cols:
            col_name = df.columns[col_idx]
            header_val = header_row.iloc[col_idx]
            
            # Check if this column has numeric data
            numeric_data = pd.to_numeric(df_data.iloc[:, col_idx], errors='coerce').dropna()
            
            print(f"  Column {col_idx} ('{col_name}' | header: '{header_val}'):")
            print(f"    Numeric values: {len(numeric_data)}")
            if len(numeric_data) > 0:
                print(f"    Sample values: {numeric_data.head().tolist()}")
                print(f"    Value range: {numeric_data.min()} - {numeric_data.max()}")
                
                # Check if values look like power generation (large numbers)
                if numeric_data.max() > 1000000:  # > 1 million (likely MWh)
                    print(f"    🔍 Could be power generation data (large values)")
                elif 0 < numeric_data.max() < 10:  # Small decimal values
                    print(f"    🌿 Could be emission factor data (small decimal values)")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error investigating missing years: {e}")
        import traceback
        traceback.print_exc()
        return False

def propose_corrected_mapping():
    """Propose a corrected column mapping based on investigation"""
    print("💡 PROPOSING CORRECTED COLUMN MAPPING")
    print("=" * 50)
    
    try:
        df = pd.read_excel('Australia_details.xlsx')
        header_row = df.iloc[0]
        
        print("📋 PROPOSED MAPPING BASED ON HEADER ANALYSIS:")
        
        # Manual mapping based on header analysis
        proposed_mapping = {}
        
        for i, header_val in enumerate(header_row):
            header_str = str(header_val).lower()
            
            if '2020' in header_str and 'gross' in header_str:
                proposed_mapping[2020] = {'gross_col': i, 'emission_col': None}
                print(f"  2020 Gross Power: Column {i}")
            elif '2020' in header_str and 'emission' in header_str:
                if 2020 in proposed_mapping:
                    proposed_mapping[2020]['emission_col'] = i
                else:
                    proposed_mapping[2020] = {'gross_col': None, 'emission_col': i}
                print(f"  2020 Emission Factor: Column {i}")
            elif '2021' in header_str and 'gross' in header_str:
                proposed_mapping[2021] = {'gross_col': i, 'emission_col': None}
                print(f"  2021 Gross Power: Column {i}")
            elif '2021' in header_str and 'emission' in header_str:
                if 2021 in proposed_mapping:
                    proposed_mapping[2021]['emission_col'] = i
                else:
                    proposed_mapping[2021] = {'gross_col': None, 'emission_col': i}
                print(f"  2021 Emission Factor: Column {i}")
        
        print(f"\n📊 Proposed mapping: {proposed_mapping}")
        
        # Test the proposed mapping with Bayswater data
        if proposed_mapping:
            print("\n🧪 TESTING PROPOSED MAPPING WITH BAYSWATER:")
            df_data = df.iloc[1:].reset_index(drop=True)
            bayswater_mask = df_data['Plant Name'].str.contains('Bayswater', case=False, na=False)
            
            if bayswater_mask.any():
                row = df_data[bayswater_mask].iloc[0]
                
                for year, cols in proposed_mapping.items():
                    print(f"  Year {year}:")
                    if cols['gross_col'] is not None:
                        gross_val = row.iloc[cols['gross_col']]
                        print(f"    Gross power (col {cols['gross_col']}): {gross_val}")
                    if cols['emission_col'] is not None:
                        emission_val = row.iloc[cols['emission_col']]
                        print(f"    Emission factor (col {cols['emission_col']}): {emission_val}")
        
        return proposed_mapping
        
    except Exception as e:
        print(f"❌ Error proposing corrected mapping: {e}")
        import traceback
        traceback.print_exc()
        return {}

if __name__ == "__main__":
    success1 = investigate_missing_years()
    mapping = propose_corrected_mapping()
    
    if success1:
        print(f"\n🎉 Investigation completed successfully!")
        if mapping:
            print(f"💡 Found potential mapping for missing years: {list(mapping.keys())}")
        else:
            print(f"⚠️ No clear mapping found for 2020/2021 data")
    else:
        print(f"\n❌ Investigation failed!")
