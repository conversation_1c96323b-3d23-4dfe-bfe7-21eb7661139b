#!/usr/bin/env python3
"""
Test that the Australia Excel integration path is fixed
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.australia_excel_integration import get_australia_integrator

def test_path_fix():
    """Test that the Excel file can be found with the corrected path"""
    print("🧪 Testing Australia Excel Integration Path Fix")
    print("=" * 50)
    
    try:
        # Get the integrator
        integrator = get_australia_integrator()
        
        # Check if data is available
        if integrator.is_data_available():
            print("✅ Excel file found and loaded successfully!")
            print(f"   📊 Data shape: {integrator.df.shape}")
            
            # Test a known plant
            test_plant = "Bayswater Power Station"
            result = integrator.find_plant_data(test_plant)
            
            if result:
                print(f"✅ Successfully found data for {test_plant}")
                print(f"   🏢 Entity: {result['entity_name']}")
                print(f"   📅 Years available: {result['years_available']}")
                
                if result['gross_power_generation']:
                    print(f"   📈 Gross power generation data: {len(result['gross_power_generation'])} years")
                    
                if result['emission_factor']:
                    print(f"   🌿 Emission factor data: {len(result['emission_factor'])} years")
                    
                return True
            else:
                print(f"❌ Could not find data for {test_plant}")
                return False
        else:
            print("❌ Excel file not found or could not be loaded")
            return False
            
    except Exception as e:
        print(f"❌ Error during path test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_path_fix()
    if success:
        print("\n🎉 Path fix test passed!")
    else:
        print("\n❌ Path fix test failed!")
