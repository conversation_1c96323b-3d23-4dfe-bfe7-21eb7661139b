# Financial Pipeline Message Format Guide

## 🎯 **SIMPLIFIED MESSAGE FORMAT**

The financial pipeline should send **ONE SIMPLE JSON** to complete the workflow.

## ✅ **Required Message Format**

### **Message Body (JSON):**
```json
{
  "status": "successful completion"
}
```

### **SQS Message Attributes (Optional but Recommended):**
```python
MessageAttributes={
    'MessageType': {
        'StringValue': 'financial_pipeline_completion',
        'DataType': 'String'
    }
}
```

## 🚀 **Complete Example**

### **Python Code for Financial Pipeline:**
```python
import boto3
import json

def send_completion_to_technical_pipeline():
    """
    Send simple completion message to technical pipeline
    """
    
    # Create SQS client
    sqs_client = boto3.client(
        'sqs',
        aws_access_key_id='AKIA5H4ZJQSCTI6WEK4E',  # Your credentials
        aws_secret_access_key='YOUR_SECRET_KEY',
        region_name='ap-south-1'
    )
    
    # SIMPLE MESSAGE BODY
    message_body = {
        "status": "successful completion"
    }
    
    # Send message
    response = sqs_client.send_message(
        QueueUrl='https://sqs.ap-south-1.amazonaws.com/910317683845/transition.fifo',
        MessageBody=json.dumps(message_body),
        MessageGroupId="financial_completion",
        MessageDeduplicationId=f"completion_{int(time.time())}",
        MessageAttributes={
            'MessageType': {
                'StringValue': 'financial_pipeline_completion',
                'DataType': 'String'
            }
        }
    )
    
    print(f"✅ Completion message sent: {response['MessageId']}")
```

### **Alternative Status Values:**
```json
{"status": "successful completion"}
{"status": "completed"}
{"status": "success"}
{"status": "processing complete"}
{"status": "failed"}
{"status": "error occurred"}
```

## 🔧 **Our Parsing Implementation**

### **How We Detect Completion Messages:**
```python
# We accept messages in two ways:
# 1. With MessageType attribute (recommended)
# 2. Any JSON with "status" field (simple)

message_type = message_attributes.get('MessageType', {}).get('StringValue', '')
is_completion = (
    message_type == 'financial_pipeline_completion' or  # Attribute-based
    'status' in message_body  # Simple JSON with status field
)
```

### **How We Process Simple Messages:**
```python
# For simple format - just status field
financial_status = message_body.get('status', 'completed')
plant_name = 'Financial Pipeline Completion'
org_name = 'Financial System'
uid = 'financial_completion'

# Send final completion to backend team
send_backend_completion_message(
    plant_name=plant_name,
    extraction_status=f"Extraction Completed - {financial_status}",
    session_id=session_id
)
```

## 📊 **Complete Workflow**

### **1. Technical Pipeline Sends to Financial Pipeline:**
```json
{
  "message_type": "financial_pipeline_trigger",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "session_id": "session_123",
  "data": {
    "org_name": "Hokuriku Electric Power Co.",
    "plant_name": "Toyama Shinko power station",
    "country": "Japan",
    "uid": "ORG_JA_7DFF75_52223470"
  }
}
```

### **2. Financial Pipeline Processes (Your Work)**
- Receive the message
- Perform financial analysis
- Complete processing

### **3. Financial Pipeline Sends Back (SIMPLE):**
```json
{
  "status": "successful completion"
}
```

### **4. Technical Pipeline Receives and Forwards:**
- Automatically detects completion message
- Sends final notification to backend team
- Deletes message from queue

## ✅ **Benefits of Simple Format**

### **For Financial Pipeline Team:**
- ✅ **Easy to implement** - just one JSON field
- ✅ **No complex structure** required
- ✅ **Flexible status values** - any descriptive text
- ✅ **Minimal code** required

### **For Technical Pipeline:**
- ✅ **Robust parsing** - handles both simple and complex formats
- ✅ **Automatic detection** - works with or without attributes
- ✅ **Error resistant** - continues working even with variations
- ✅ **Backward compatible** - supports future format changes

## 🎯 **Summary**

**Financial Pipeline needs to send:**
1. ✅ **JSON message body**: `{"status": "successful completion"}`
2. ✅ **To SQS queue**: `transition.fifo`
3. ✅ **Optional attributes**: `MessageType: 'financial_pipeline_completion'`

**That's it!** Our system will automatically:
- Detect the completion message
- Process it within 30 seconds
- Send final notification to backend team
- Clean up the queue

## 🚀 **Ready to Use**

The financial pipeline can now send the simplest possible completion message, and our system will handle everything else automatically!

**Example for testing:**
```bash
# Send test message
aws sqs send-message \
  --queue-url "https://sqs.ap-south-1.amazonaws.com/910317683845/transition.fifo" \
  --message-body '{"status":"successful completion"}' \
  --message-group-id "test_completion" \
  --message-deduplication-id "test_$(date +%s)"
```
