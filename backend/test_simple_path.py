#!/usr/bin/env python3
"""
Simple test for Australia Excel path fix without importing the full pipeline
"""

import pandas as pd
import os
from pathlib import Path

def test_excel_path():
    """Test that the Excel file can be found at the correct path"""
    print("🧪 Testing Australia Excel File Path")
    print("=" * 40)
    
    # Test the corrected path logic
    # Path: backend/src/agent/australia_excel_integration.py -> backend/Australia_details.xlsx
    current_file = Path(__file__)  # backend/test_simple_path.py
    excel_path = current_file.parent / "Australia_details.xlsx"  # backend/Australia_details.xlsx
    
    print(f"📁 Current file: {current_file}")
    print(f"📊 Excel path: {excel_path}")
    print(f"📍 Excel exists: {excel_path.exists()}")
    
    if excel_path.exists():
        try:
            # Try to load the Excel file
            df = pd.read_excel(excel_path)
            print(f"✅ Excel file loaded successfully!")
            print(f"   📊 Shape: {df.shape}")
            print(f"   📋 Columns: {list(df.columns)[:5]}...")  # Show first 5 columns
            
            # Check for Plant Name column
            if 'Plant Name' in df.columns:
                print(f"✅ 'Plant Name' column found")
                
                # Show some plant names
                plant_names = df['Plant Name'].dropna().head(5).tolist()
                print(f"   🏭 Sample plants: {plant_names}")
                
                # Test Bayswater specifically
                bayswater_matches = df[df['Plant Name'].str.contains('Bayswater', case=False, na=False)]
                if not bayswater_matches.empty:
                    print(f"✅ Found Bayswater Power Station in Excel")
                    print(f"   🏢 Entity: {bayswater_matches.iloc[0]['Entity Name']}")
                else:
                    print(f"⚠️ Bayswater Power Station not found in Excel")
                
                return True
            else:
                print(f"❌ 'Plant Name' column not found in Excel")
                return False
                
        except Exception as e:
            print(f"❌ Error loading Excel file: {e}")
            return False
    else:
        print(f"❌ Excel file not found at: {excel_path}")
        return False

def test_integration_path():
    """Test the path logic used in the integration module"""
    print(f"\n🧪 Testing Integration Module Path Logic")
    print("=" * 40)
    
    # Simulate the path logic from australia_excel_integration.py
    # Path: backend/src/agent/australia_excel_integration.py -> backend/Australia_details.xlsx
    integration_file = Path(__file__).parent / "src" / "agent" / "australia_excel_integration.py"
    excel_path = integration_file.parent.parent.parent / "Australia_details.xlsx"
    
    print(f"📁 Integration file (simulated): {integration_file}")
    print(f"📊 Excel path (from integration): {excel_path}")
    print(f"📍 Excel exists: {excel_path.exists()}")
    
    return excel_path.exists()

if __name__ == "__main__":
    success1 = test_excel_path()
    success2 = test_integration_path()
    
    if success1 and success2:
        print(f"\n🎉 All path tests passed!")
    else:
        print(f"\n❌ Some path tests failed")
