#!/usr/bin/env python3
"""
Test the current integration to see what's actually happening
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_current_integration():
    """Test the current integration"""
    print("🧪 Testing Current Integration")
    print("=" * 40)
    
    try:
        # Import the integration function
        from agent.australia_excel_integration import integrate_australia_excel_data_for_unit
        
        # Mock unit data
        mock_unit_data = {
            "sk": "unit#coal#1#plant#1",
            "unit_number": "1",
            "plant_id": "1",
            "capacity": "660",
            "capacity_unit": "MW",
            "technology": "Super Critical"
        }
        
        print("📋 Testing integration with Bayswater Power Station")
        print()
        
        # Apply integration
        result = integrate_australia_excel_data_for_unit(
            unit_info=mock_unit_data,
            plant_name="Bayswater Power Station",
            country="Australia",
            session_id="test_current"
        )
        
        print(f"🔍 Result keys: {list(result.keys())}")
        print()
        
        # Check if gross_power_generation exists
        if "gross_power_generation" in result:
            gpg = result["gross_power_generation"]
            print(f"✅ gross_power_generation found: {len(gpg)} entries")
            for entry in sorted(gpg, key=lambda x: int(x['year'])):
                print(f"   {entry['year']}: {entry['value']} MWh")
        else:
            print(f"❌ gross_power_generation NOT found in result")
            
        # Check if emission_factor exists
        if "emission_factor" in result:
            ef = result["emission_factor"]
            print(f"✅ emission_factor found: {len(ef)} entries")
            for entry in sorted(ef, key=lambda x: int(x['year'])):
                print(f"   {entry['year']}: {entry['value']} tCO2/MWh")
        else:
            print(f"❌ emission_factor NOT found in result")
            
        # Check integration metadata
        if "australia_excel_integration" in result:
            meta = result["australia_excel_integration"]
            print(f"✅ Integration metadata found:")
            print(f"   Excel entity: {meta.get('excel_entity_name', 'N/A')}")
            print(f"   Excel plant: {meta.get('excel_plant_name', 'N/A')}")
            print(f"   Years available: {meta.get('years_available', [])}")
        else:
            print(f"❌ Integration metadata NOT found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during integration test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_current_integration()
