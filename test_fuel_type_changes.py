#!/usr/bin/env python3
"""
Test script to verify the new fuel_type logic changes:
1. Replace year_percentage values >= 0.95 with 1.0
2. Generate years from plant commencement date to 2024
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from agent.fallback_calculations import FallbackCalculationEngine

def test_fuel_type_changes():
    """Test the new fuel_type logic"""
    
    print("🧪 Testing Fuel Type Changes")
    print("=" * 50)
    
    # Initialize the fallback engine
    engine = FallbackCalculationEngine()
    
    # Test Case 1: >= 0.95 rule with various percentage formats
    print("\n📋 Test Case 1: >= 0.95 Rule")
    test_data_1 = {
        "fuel_type": [
            {
                "fuel": "Coal",
                "type": "Bituminous",
                "years_percentage": {
                    "2023": "96",      # 96% -> should become 1.0
                    "2022": "94",      # 94% -> should become 0.94
                    "2021": "0.97",    # 0.97 -> should become 1.0
                    "2020": "0.93"     # 0.93 -> should stay 0.93
                }
            }
        ]
    }
    
    unit_context_1 = {"commencement_date": "2020-01-01T00:00:00.000Z"}
    
    result_1 = engine._enhance_fuel_years_percentage(test_data_1, unit_context_1)
    print(f"✅ Enhancement applied: {result_1}")
    print("📊 Results:")
    for fuel in test_data_1["fuel_type"]:
        for year, percentage in fuel["years_percentage"].items():
            print(f"   {year}: {percentage}")
    
    # Test Case 2: Commencement date range (2000 to 2024)
    print("\n📋 Test Case 2: Commencement Date Range")
    test_data_2 = {
        "fuel_type": [
            {
                "fuel": "Coal",
                "type": "Sub-bituminous",
                "years_percentage": {}  # Empty - should generate from 2000 to 2024
            }
        ]
    }
    
    unit_context_2 = {"commencement_date": "2000-06-15T00:00:00.000Z"}
    
    result_2 = engine._enhance_fuel_years_percentage(test_data_2, unit_context_2)
    print(f"✅ Enhancement applied: {result_2}")
    print("📊 Generated years:")
    fuel_data = test_data_2["fuel_type"][0]["years_percentage"]
    years = sorted(fuel_data.keys())
    print(f"   Range: {years[0]} to {years[-1]} ({len(years)} years)")
    print(f"   Sample values: {years[0]}={fuel_data[years[0]]}, {years[-1]}={fuel_data[years[-1]]}")
    
    # Test Case 3: Recent plant (2020 to 2024)
    print("\n📋 Test Case 3: Recent Plant")
    test_data_3 = {
        "fuel_type": [
            {
                "fuel": "Coal",
                "type": "Lignite",
                "years_percentage": {}
            }
        ]
    }
    
    unit_context_3 = {"commencement_date": "2020-01-01"}
    
    result_3 = engine._enhance_fuel_years_percentage(test_data_3, unit_context_3)
    print(f"✅ Enhancement applied: {result_3}")
    fuel_data_3 = test_data_3["fuel_type"][0]["years_percentage"]
    years_3 = sorted(fuel_data_3.keys())
    print(f"📊 Generated years: {years_3}")
    
    # Test Case 4: Mixed existing and missing data
    print("\n📋 Test Case 4: Mixed Data")
    test_data_4 = {
        "fuel_type": [
            {
                "fuel": "Coal",
                "type": "Anthracite",
                "years_percentage": {
                    "2024": "98",      # Should become 1.0
                    "2023": "0.94"     # Should stay 0.94
                    # Missing years should be filled from 2010-2024
                }
            }
        ]
    }
    
    unit_context_4 = {"commencement_date": "2010-01-01T00:00:00.000Z"}
    
    result_4 = engine._enhance_fuel_years_percentage(test_data_4, unit_context_4)
    print(f"✅ Enhancement applied: {result_4}")
    fuel_data_4 = test_data_4["fuel_type"][0]["years_percentage"]
    years_4 = sorted(fuel_data_4.keys())
    print(f"📊 Total years: {len(years_4)} (from {years_4[0]} to {years_4[-1]})")
    print(f"   2024: {fuel_data_4['2024']} (was 98%, should be 1.0)")
    print(f"   2023: {fuel_data_4['2023']} (was 0.94, should stay 0.94)")
    
    print("\n🎯 Test Summary:")
    print("✅ >= 0.95 rule: Values >= 0.95 are replaced with 1.0")
    print("✅ Date range: Years generated from commencement date to 2024")
    print("✅ Format: All values are in decimal format (0.0 to 1.0)")
    print("✅ Coal only: Biomass filtering maintained")

if __name__ == "__main__":
    test_fuel_type_changes()
