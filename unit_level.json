{"sk": "unit#plant_type#unit_id#plant#plant_id", "auxiliary_power_consumed": [{"value": "The auxiliary (AUX) energy i.e energy consumed internally by the power plant its for its day-to-day operations. It is represented as a percentage of the gross energy generated by the plant/unnit.", "year": "Year for which auxiliary power data is reported"}], "boiler_type": "For coal plants: Mechanical Stoker-Fired, Pulverized Coal, Fluidized Bed Combustion, Cyclone. For other plants: original boiler type description", "capacity": "Unit-wise installed capacity of the plant in megawatts (MW)", "capacity_unit": "Measuring Unit for installed capacity, typically 'MW'", "commencement_date": "The date of commercial operation of a specific unit. Represented in (yyyy-mm-ddThh:mm:ss:ms). Ex. 2012-1-01T00:00:00.000Z", "efficiency_loss_biomass_cofiring": "The reduction in the coal plant efficiency (in %) as a result of retrofitting the existing infrastructure/technology to accomodate biomass cofiring. ", "emission_factor": [{"value": "The amount by weight of CO2 emissions released by the unit/plant per kiliwatt-hour (kWh) generation of energy. Represented in (kg CO2e/kWh)", "year": "Year of the emission factor data"}], "fuel_type": [{"fuel": "Coal only - exclude biomass details if present. Primary fuel source for the coal power plant unit.", "type": "Coal subcategory only - bituminous, sub-bituminous, lignite, anthracite, etc. Do not include biomass types like wood pellets, palm/coconut kernel shells (PKS), crop husks, etc.", "years_percentage": {"Specified year the fuel was used": "The percentage of coal fuel used in a particular unit of a plant. If biomass cofiring is present, only report the coal percentage, not biomass percentage."}}], "gross_power_generation": [{"value": "Total energy generated by the unit/plant in a Financial Year", "year": "Year of the power generation data"}], "heat_rate": "Station Heat Rate (SHR) is a key performance metric that measures the efficiency of a power plant. Specifically, it indicates how much fuel energy is required to generate one unit of electricity.Station Heat Rate is the amount of heat energy input (usually in kJ or kcal) required to produce one kilowatt-hour (kWh) of electrical energy output.The SHR varies by plant technology (Advanced Ultra Supercritical, Ultra Supercritical, Supercritical and Sub-Critical. This data is required for each technology relative to the country the search is being made.", "heat_rate_unit": "Station Heat Rate is the amount of heat energy input (usually in kJ or kcal) required to produce one kilowatt-hour (kWh) of electrical energy output.", "PAF": [{"value": "Plant Availability Factor (PAF) is a performance metric used in power generation to indicate how often a power plant is available to generate electricity over a specific period, regardless of whether it is actually producing electricity during that time.", "year": "Year of the PAF data"}], "plant_id": "Unique identifier for the plant", "plf": [{"value": "The yearly Plant Load factor (PLF) for every unit of the plant. Represented in %.", "year": "Year of the PLF data"}], "remaining_useful_life": "The end-of-life of a specific unit. Represented in (yyyy-mm-ddThh:mm:ss:ms). Ex. 2012-1-01T00:00:00.000Z", "selected_biomass_type": "Selected biomass type used for cofiring", "selected_coal_type": "Selected coal type used in the unit", "technology": "For coal plants: Subcritical, Supercritical, Ultra-Supercritical, Advanced Ultra-Supercritical. For other plants: Natural Gas - Single/Open Cycle, Combined/Closed Cycle. Biomass - Fluidized Bed Reactor, Direct Combustion, Boiler Conversion", "unit": "Unit efficiency measurement in percentage (%)", "coal_unit_efficiency": "Unit specific efficiency of the coal power plant unit", "unit_lifetime": "Total operational lifetime of the unit in years.", "unit_number": "Unit number labeling.", "pk": "default null", "annual_operational_hours": "default null", "blending_percentage_of_biomass": "default null", "emission_factor_coal": "default null", "emission_factor_unit": "default null", "fgds_status": "default null", "ramp_down_rate": "default null", "ramp_up_rate": "default null"}