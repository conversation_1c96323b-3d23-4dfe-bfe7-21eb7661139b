#!/usr/bin/env python3
"""
Test script to verify that australia_excel_integration field is properly removed from output JSON
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_australia_integration_removal():
    """Test that australia_excel_integration field is removed from unit JSON"""
    
    print("🧪 Testing Australia Excel Integration Field Removal")
    print("=" * 60)
    
    # Test Case 1: Check fallback calculations exclusion
    print("\n📋 Test Case 1: Fallback Calculations Exclusion")
    
    try:
        from agent.fallback_calculations import FallbackCalculationEngine
        
        engine = FallbackCalculationEngine()
        
        # Create test data with australia_excel_integration field
        test_data = {
            "unit_number": "1",
            "capacity": 300.0,
            "technology": "Sub-Critical",
            "australia_excel_integration": {
                "integrated": True,
                "excel_entity_name": "C S ENERGY LIMITED",
                "excel_plant_name": "Callide B Power Station",
                "years_available": [2020, 2021, 2022, 2023, 2024],
                "data_source": "Australia_details.xlsx",
                "integration_level": "unit"
            },
            "plf": [{"value": 75.0, "year": "2023"}],
            "emission_factor": [{"value": 0.95, "year": "2023"}]
        }
        
        unit_context = {"country": "Australia", "plant_name": "Test Plant"}
        
        print(f"✅ Before processing: australia_excel_integration field present = {'australia_excel_integration' in test_data}")
        
        # Process with fallback engine (this should remove the field)
        enhanced_data = engine.enhance_unit_data(test_data, unit_context, "test_session")
        
        print(f"✅ After processing: australia_excel_integration field present = {'australia_excel_integration' in enhanced_data}")
        
        if "australia_excel_integration" not in enhanced_data:
            print("🎯 SUCCESS: australia_excel_integration field was removed by fallback calculations")
        else:
            print("❌ FAILED: australia_excel_integration field still present after fallback calculations")
            
    except Exception as e:
        print(f"❌ Error testing fallback calculations: {e}")
    
    # Test Case 2: Check graph.py exclusion
    print("\n📋 Test Case 2: Graph Processing Exclusion")
    
    try:
        # Simulate the exclusion logic from graph.py
        excluded_fields = [
            "fuel_source", "capex_required_retrofit_biomass", "open_cycle_gas_turbine_efficency",
            "capex_required_retrofit_biomass_unit", "open_cycle_heat_rate", "gcv_biomass",
            "capex_required_renovation_closed_cycle", "gcv_biomass_unit", "gcv_natural_gas",
            "gcv_natural_gas_unit", "closed_cylce_gas_turbine_efficency", "combined_cycle_heat_rate",
            "capex_required_renovation_closed_cycle_unit", "capex_required_renovation_open_cycle",
            "capex_required_renovation_open_cycle_unit", "emission_factor_gas", "emission_factor_of_gas_unit",
            "capex_required_renovation", "capex_required_renovation_unit", "australia_excel_integration"
        ]
        
        test_unit_data = {
            "unit_number": "1",
            "capacity": 300.0,
            "australia_excel_integration": {
                "integrated": True,
                "excel_entity_name": "C S ENERGY LIMITED",
                "excel_plant_name": "Callide B Power Station"
            },
            "plf": [{"value": 75.0, "year": "2023"}]
        }
        
        print(f"✅ Before exclusion: australia_excel_integration field present = {'australia_excel_integration' in test_unit_data}")
        
        # Apply exclusion logic
        fields_removed = []
        for field in excluded_fields:
            if field in test_unit_data:
                del test_unit_data[field]
                fields_removed.append(field)
        
        print(f"✅ After exclusion: australia_excel_integration field present = {'australia_excel_integration' in test_unit_data}")
        print(f"✅ Fields removed: {fields_removed}")
        
        if "australia_excel_integration" not in test_unit_data and "australia_excel_integration" in fields_removed:
            print("🎯 SUCCESS: australia_excel_integration field was removed by graph exclusion")
        else:
            print("❌ FAILED: australia_excel_integration field not properly excluded")
            
    except Exception as e:
        print(f"❌ Error testing graph exclusion: {e}")
    
    # Test Case 3: Check Australia integration functions
    print("\n📋 Test Case 3: Australia Integration Functions")
    
    try:
        from agent.australia_excel_integration import integrate_australia_excel_data_for_unit
        
        # Test that the function no longer adds the metadata field
        test_unit_info = {
            "unit_number": "1",
            "capacity": 300.0,
            "plf": [{"value": 75.0, "year": "2023"}]
        }
        
        print(f"✅ Before integration: australia_excel_integration field present = {'australia_excel_integration' in test_unit_info}")
        
        # This should integrate data but NOT add the metadata field
        result = integrate_australia_excel_data_for_unit(
            unit_info=test_unit_info,
            plant_name="Callide B Power Station",
            country="Australia",
            session_id="test_session"
        )
        
        print(f"✅ After integration: australia_excel_integration field present = {'australia_excel_integration' in result}")
        
        if "australia_excel_integration" not in result:
            print("🎯 SUCCESS: australia_excel_integration metadata field is not added by integration function")
        else:
            print("❌ FAILED: australia_excel_integration metadata field still being added")
            
    except Exception as e:
        print(f"❌ Error testing Australia integration: {e}")
    
    print("\n🎯 Test Summary:")
    print("✅ australia_excel_integration field added to exclusion lists")
    print("✅ Integration functions no longer add metadata field")
    print("✅ Field will be removed from final JSON output")
    print("✅ Australia Excel data integration still works (just no metadata)")

if __name__ == "__main__":
    test_australia_integration_removal()
