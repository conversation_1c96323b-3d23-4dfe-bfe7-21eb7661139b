import { InputForm } from "./InputForm";

interface WelcomeScreenProps {
  handleSubmit: (
    submittedInputValue: string,
    effort: string,
    model: string
  ) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  handleSubmit,
  onCancel,
  isLoading,
}) => (
  <div className="flex flex-col items-center justify-center text-center px-4 flex-1 w-full max-w-3xl mx-auto gap-4">
    <div>
      <h1 className="text-5xl md:text-6xl font-semibold text-neutral-100 mb-3">
        Power Plant Info
      </h1>
      <p className="text-xl md:text-2xl text-neutral-400 mb-4">
        Enter a power plant name to extract structured information
      </p>
      <div className="bg-neutral-700/50 p-4 rounded-lg text-left text-sm text-neutral-300 mb-4">
        <p className="font-semibold mb-2">This tool will extract:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Ownership type (public/private/joint-venture)</li>
          <li>Location (country and province)</li>
          <li>Parent organization details</li>
          <li>Financial information (currency, fiscal year)</li>
          <li>Technical specifications (plant types)</li>
          <li>Power Purchase Agreement structure</li>
          <li>And more...</li>
        </ul>
      </div>
    </div>
    <div className="w-full mt-2">
      <InputForm
        onSubmit={handleSubmit}
        isLoading={isLoading}
        onCancel={onCancel}
        hasHistory={false}
      />
    </div>
    <p className="text-xs text-neutral-500">
      Powered by Google Gemini and LangChain LangGraph.
    </p>
  </div>
);
