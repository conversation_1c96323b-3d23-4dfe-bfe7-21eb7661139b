import { useStream } from "@langchain/langgraph-sdk/react";
import type { Message } from "@langchain/langgraph-sdk";
import { useState, useEffect, useRef, useCallback } from "react";
import { ProcessedEvent } from "./components/ActivityTimeline";
import { WelcomeScreen } from "./components/WelcomeScreen";
import { ChatMessagesView } from "./components/ChatMessagesView";

export default function App() {
  const [processedEventsTimeline, setProcessedEventsTimeline] = useState<
    ProcessedEvent[]
  >([]);
  const [historicalActivities, setHistoricalActivities] = useState<
    Record<string, ProcessedEvent[]>
  >({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const hasFinalizeEventOccurredRef = useRef(false);

  const thread = useStream<{
    messages: Message[];
    initial_search_query_count: number;
    max_research_loops: number;
    reasoning_model: string;
  }>({
    apiUrl: import.meta.env.DEV
      ? "http://localhost:2024"
      : "http://localhost:8123",
    assistantId: "agent",
    messagesKey: "messages",
    onFinish: (event: any) => {
      console.log(event);
    },
    onUpdateEvent: (event: any) => {
      let processedEvent: ProcessedEvent | null = null;
      if (event.generate_query) {
        processedEvent = {
          title: "Generating Search Queries",
          data: event.generate_query.query_list.join(", "),
        };
      } else if (event.web_research) {
        const sources = event.web_research.sources_gathered || [];
        const numSources = sources.length;
        const uniqueLabels = [
          ...new Set(sources.map((s: any) => s.label).filter(Boolean)),
        ];
        const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
        processedEvent = {
          title: "Web Research",
          data: `Gathered ${numSources} sources. Related to: ${
            exampleLabels || "N/A"
          }.`,
        };
      } else if (event.reflection) {
        processedEvent = {
          title: "Reflection",
          data: event.reflection.is_sufficient
            ? "Search successful, generating final answer."
            : `Need more information, searching for ${event.reflection.follow_up_queries?.join(
                ", "
              ) || "additional information"}`,
        };
      } else if (event.finalize_answer) {
        processedEvent = {
          title: "Finalizing Answer",
          data: "Composing and presenting the final answer.",
        };
        hasFinalizeEventOccurredRef.current = true;
      } else if (event.process_all_units) {
        processedEvent = {
          title: "Unit Processing",
          data: `Processing ${event.process_all_units.units_count || 'multiple'} power plant units in parallel batches.`,
        };
      } else if (event.unit_web_research_isolated || event.unit_generate_query_isolated) {
        const unitNumber = event.unit_web_research_isolated?.unit_number || event.unit_generate_query_isolated?.unit_number || 'Unknown';
        processedEvent = {
          title: `Unit ${unitNumber} Research`,
          data: event.unit_web_research_isolated 
            ? `Collecting technical data for Unit ${unitNumber}.`
            : `Generating research queries for Unit ${unitNumber}.`,
        };
      } else if (event.unit_finalize_isolated) {
        const unitNumber = event.unit_finalize_isolated?.unit_number || 'Unknown';
        const hasData = event.unit_finalize_isolated?.technical_data_available;
        processedEvent = {
          title: `Unit ${unitNumber} Complete`,
          data: hasData 
            ? `Successfully extracted technical data for Unit ${unitNumber}.`  
            : `Unit ${unitNumber} processing completed.`,
        };
      } else if (event.collect_smart_batch_results) {
        const unitsProcessed = event.collect_smart_batch_results?.units_processed || 0;
        processedEvent = {
          title: "Batch Processing Complete",
          data: `Successfully processed ${unitsProcessed} units. Compiling final results.`,
        };
      } else if (event.detect_multiple_plants) {
        const plantsDetected = event.detect_multiple_plants?.plants_detected || 1;
        const isMultiPlant = event.detect_multiple_plants?.multi_plant_processing || false;
        processedEvent = {
          title: "Multi-Plant Detection",
          data: isMultiPlant 
            ? `Detected ${plantsDetected} plants. Initiating parallel plant processing.`
            : `Single plant confirmed. Proceeding with unit analysis.`,
        };
      } else if (event.process_multiple_plants) {
        const plantCount = event.process_multiple_plants?.plants_detected || 0;
        processedEvent = {
          title: "Multi-Plant Processing",
          data: `Processing ${plantCount} plants in parallel. Each plant will be analyzed for units and technical data.`,
        };
      } else if (event.process_single_plant_isolated) {
        const plantName = event.process_single_plant_isolated?.current_plant_name || 'Unknown Plant';
        const plantNumber = event.process_single_plant_isolated?.plant_number || 'Unknown';
        processedEvent = {
          title: `Plant ${plantNumber} Processing`,
          data: `Analyzing ${plantName} for plant-level and unit-level data.`,
        };
      } else if (event.collect_multi_plant_results) {
        const plantsProcessed = event.collect_multi_plant_results?.plants_processed || 0;
        const totalUnits = event.collect_multi_plant_results?.total_units_processed || 0;
        processedEvent = {
          title: "Multi-Plant Analysis Complete",
          data: `Successfully analyzed ${plantsProcessed} plants with ${totalUnits} total units. Compiling comprehensive results.`,
        };
      } else if (event.extract_images_parallel) {
        const imageCount = event.extract_images_parallel?.s3_image_urls?.length || 0;
        const isComplete = event.extract_images_parallel?.image_extraction_complete || false;
        const hasError = event.extract_images_parallel?.image_extraction_error;
        
        if (hasError && hasError !== "") {
          processedEvent = {
            title: "Image Extraction Failed",
            data: `Unable to extract images: ${hasError}`,
          };
        } else if (isComplete && imageCount > 0) {
          processedEvent = {
            title: "Image Extraction Complete",
            data: `Successfully extracted and uploaded ${imageCount} images to cloud storage.`,
          };
        } else if (isComplete && imageCount === 0) {
          processedEvent = {
            title: "Image Extraction Complete",
            data: "Image extraction completed, but no suitable images were found.",
          };
        } else {
          processedEvent = {
            title: "Extracting Images",
            data: "Searching and processing power plant images in parallel...",
          };
        }
      }
      if (processedEvent) {
        setProcessedEventsTimeline((prevEvents) => [
          ...prevEvents,
          processedEvent!,
        ]);
      }
    },
  });

  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollViewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollViewport) {
        scrollViewport.scrollTop = scrollViewport.scrollHeight;
      }
    }
  }, [thread.messages]);

  useEffect(() => {
    if (
      hasFinalizeEventOccurredRef.current &&
      !thread.isLoading &&
      thread.messages.length > 0
    ) {
      const lastMessage = thread.messages[thread.messages.length - 1];
      if (lastMessage && lastMessage.type === "ai" && lastMessage.id) {
        setHistoricalActivities((prev) => ({
          ...prev,
          [lastMessage.id!]: [...processedEventsTimeline],
        }));
      }
      hasFinalizeEventOccurredRef.current = false;
    }
  }, [thread.messages, thread.isLoading, processedEventsTimeline]);

  const handleSubmit = useCallback(
    (submittedInputValue: string, effort: string, model: string) => {
      if (!submittedInputValue.trim()) return;
      setProcessedEventsTimeline([]);
      hasFinalizeEventOccurredRef.current = false;

      // convert effort to, initial_search_query_count and max_research_loops
      // low means max 1 loop and 1 query
      // medium means max 3 loops and 3 queries
      // high means max 10 loops and 5 queries
      let initial_search_query_count = 0;
      let max_research_loops = 0;
      switch (effort) {
        case "low":
          initial_search_query_count = 1;
          max_research_loops = 1;
          break;
        case "medium":
          initial_search_query_count = 3;
          max_research_loops = 3;
          break;
        case "high":
          initial_search_query_count = 5;
          max_research_loops = 10;
          break;
      }

      const newMessages: Message[] = [
        ...(thread.messages || []),
        {
          type: "human",
          content: submittedInputValue,
          id: Date.now().toString(),
        },
      ];
      thread.submit({
        messages: newMessages,
        initial_search_query_count: initial_search_query_count,
        max_research_loops: max_research_loops,
        reasoning_model: model,
      });
    },
    [thread]
  );

  const handleCancel = useCallback(() => {
    thread.stop();
    window.location.reload();
  }, [thread]);

  return (
    <div className="flex h-screen bg-neutral-800 text-neutral-100 font-sans antialiased">
      <main className="flex-1 flex flex-col overflow-hidden max-w-4xl mx-auto w-full">
        <div
          className={`flex-1 overflow-y-auto ${
            thread.messages.length === 0 ? "flex" : ""
          }`}
        >
          {thread.messages.length === 0 ? (
            <WelcomeScreen
              handleSubmit={handleSubmit}
              isLoading={thread.isLoading}
              onCancel={handleCancel}
            />
          ) : (
            <ChatMessagesView
              messages={thread.messages}
              isLoading={thread.isLoading}
              scrollAreaRef={scrollAreaRef}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              liveActivityEvents={processedEventsTimeline}
              historicalActivities={historicalActivities}
            />
          )}
        </div>
      </main>
    </div>
  );
}
